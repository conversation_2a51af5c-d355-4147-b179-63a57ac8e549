from flask import Flask, render_template, request, jsonify, redirect, url_for, session, flash
import google.generativeai as genai
import os
import sqlite3
import json
import requests
import secrets
from datetime import datetime, timedelta
from dotenv import load_dotenv
from urllib.parse import urlencode
from flask_session import Session  # Import Flask-Session
from database import (
    init_db,
    create_conversation,
    save_message,
    get_conversations,
    get_conversation_messages,
    update_conversation_title,
    delete_conversation,
    toggle_save_conversation,
    register_user,
    verify_user,
    get_user_by_username,
    get_user_by_email,
    get_user_by_id,
    create_conversation_for_user,
    get_user_conversations,
    get_message_count,
    get_conversation_summary,
    get_social_accounts,
    get_social_account,
    save_social_account,
    delete_social_account,
    update_user_profile,
    get_user_profile
)
from thread_manager import init_thread_db, save_thread, get_thread, delete_thread

app = Flask(__name__)
# Use a fixed secret key instead of random one to ensure sessions persist across server restarts
app.secret_key = os.getenv('SECRET_KEY', 'voyager_ai_secret_key_for_sessions')
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=30)  # Set session lifetime to 30 days

# Server-side session configuration
app.config['SESSION_TYPE'] = 'filesystem'  # Store sessions on filesystem
app.config['SESSION_FILE_DIR'] = os.path.join(os.getcwd(), 'flask_session')  # Directory to store session files
app.config['SESSION_COOKIE_SECURE'] = False  # Set to True in production with HTTPS
app.config['SESSION_COOKIE_HTTPONLY'] = True  # Prevent JavaScript access to session cookie
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'  # Restrict cookie sending to same-site requests
app.config['SESSION_COOKIE_NAME'] = 'voyager_session'  # Custom session cookie name
app.config['SESSION_USE_SIGNER'] = True  # Sign the session cookie for added security
app.config['SESSION_REFRESH_EACH_REQUEST'] = True  # Refresh the session cookie on each request
app.config['SESSION_PERMANENT'] = True  # Make sessions permanent by default

# Initialize Flask-Session
Session(app)

load_dotenv()
init_db()
init_thread_db()

# Create session directory if it doesn't exist
os.makedirs(app.config['SESSION_FILE_DIR'], exist_ok=True)

def create_test_user():
    """Create a test user if it doesn't exist"""
    test_user = get_user_by_username('testuser')
    if not test_user:
        print("Creating test user...")
        user_id = register_user('testuser', '<EMAIL>', 'password123')
        if user_id:
            print(f"Test user created with ID: {user_id}")
        else:
            print("Failed to create test user")
    else:
        print(f"Test user already exists with ID: {test_user[0]}")

# Create a custom user function for easier registration
def create_custom_user(username, email, password):
    """Create a custom user"""
    existing_user = get_user_by_username(username) or get_user_by_email(email)
    if existing_user:
        print(f"User with username '{username}' or email '{email}' already exists")
        return None

    print(f"Creating new user '{username}'...")
    user_id = register_user(username, email, password)
    if user_id:
        print(f"User '{username}' created with ID: {user_id}")
        return user_id
    else:
        print(f"Failed to create user '{username}'")
        return None

# Create the test user
create_test_user()

# Create a custom user for the current session
# This will create a user with the username 'Royox'
create_custom_user('Royox', '<EMAIL>', 'password123')

# OAuth Configuration
# Discord OAuth2 settings
DISCORD_CLIENT_ID = os.getenv('DISCORD_CLIENT_ID', '')
DISCORD_CLIENT_SECRET = os.getenv('DISCORD_CLIENT_SECRET', '')
DISCORD_REDIRECT_URI = os.getenv('DISCORD_REDIRECT_URI', 'http://localhost:5000/oauth/discord/callback')
DISCORD_API_ENDPOINT = 'https://discord.com/api/v10'

# YouTube OAuth2 settings
YOUTUBE_CLIENT_ID = os.getenv('YOUTUBE_CLIENT_ID', '')
YOUTUBE_CLIENT_SECRET = os.getenv('YOUTUBE_CLIENT_SECRET', '')
YOUTUBE_REDIRECT_URI = os.getenv('YOUTUBE_REDIRECT_URI', 'http://localhost:5000/oauth/youtube/callback')
YOUTUBE_API_ENDPOINT = 'https://www.googleapis.com'

# App configuration

# Simple in-memory cache for thread loading
thread_cache = {}
MAX_CACHE_SIZE = 10  # Maximum number of threads to cache

def init_genai():
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key:
        raise ValueError("GOOGLE_API_KEY not found in environment variables")

    print("\n=== Debug Information ===")
    print("API Key loaded:", api_key[:8] + "..." if api_key else "Not found")
    genai.configure(api_key=api_key)

    # Using Gemini 2.0 Flash directly since we know it's available
    model_name = "models/gemini-2.0-flash"
    print(f"\nUsing model: {model_name}")
    return api_key, model_name

class GeminiChatbot:
    def __init__(self, api_key: str, model_name: str):
        self.api_key = api_key
        self.model_name = model_name
        try:
            print(f"\nTrying to initialize with model: {model_name}")
            self.model = genai.GenerativeModel(model_name)
            self.restart_chat()
            print("Model initialized successfully")
        except Exception as e:
            print(f"Error initializing model: {str(e)}")
            raise

    def restart_chat(self):
        self.chat = self.model.start_chat(history=[])
        # Updated personality prompt with name
        self.chat.send_message(
            "You are Voyager, an AI assistant. Your name is Voyager. When asked about your name or identity, always identify yourself as Voyager. "
            "Begin your first response to a user with 'Hello! I'm Voyager, your AI assistant.' "
            "Be polite, friendly, and helpful in your responses. Keep your responses focused and relevant. "
            "Use a warm, conversational tone while maintaining professionalism. "
            "While you should acknowledge your name when asked, avoid introducing yourself in every message after the first one."
        )

    def generate_response(self, user_input: str, conversation_id=None) -> str:
        try:
            response = self.chat.send_message(user_input)

            # Save thread if conversation_id is provided
            if conversation_id:
                self.save_thread(conversation_id)

            return response.text
        except Exception as e:
            error_msg = f"Error generating response: {str(e)}"
            print(error_msg)
            return error_msg

    def clear_history(self):
        self.chat = self.model.start_chat(history=[])

    def get_serializable_history(self):
        """Convert chat history to a serializable format"""
        history = []
        for message in self.chat.history:
            history.append({
                'role': message.role,
                'parts': [{'text': part.text} for part in message.parts]
            })
        return history

    def load_from_serialized_history(self, history):
        """Load chat from serialized history"""
        try:
            # Create a new chat with the model
            self.chat = self.model.start_chat(history=[])

            # First send the system prompt
            system_prompt = "You are Voyager, an AI assistant. Your name is Voyager. When asked about your name or identity, always identify yourself as Voyager. Begin your first response to a user with 'Hello! I'm Voyager, your AI assistant.' Be polite, friendly, and helpful in your responses. Keep your responses focused and relevant. Use a warm, conversational tone while maintaining professionalism. While you should acknowledge your name when asked, avoid introducing yourself in every message after the first one."
            self.chat.send_message(system_prompt)

            # Validate the history format
            if not history or not isinstance(history, list):
                print(f"Invalid history format: {type(history)}")
                return False

            # Prepare a clean history list
            clean_history = []

            # First pass: identify valid messages and skip duplicates of system message
            for msg in history:
                # Skip invalid messages
                if not isinstance(msg, dict) or 'role' not in msg:
                    continue

                # Skip system messages (we already sent one)
                if msg['role'] == 'model' and 'parts' in msg and isinstance(msg['parts'], list) and len(msg['parts']) > 0:
                    if isinstance(msg['parts'][0], dict) and 'text' in msg['parts'][0]:
                        if 'You are Voyager' in msg['parts'][0]['text']:
                            # Skip system messages
                            continue

                # Add valid messages to our clean history
                clean_history.append(msg)

            # Only process the last few messages to avoid context length issues
            max_messages = 20  # Limit to last 20 messages for performance
            recent_messages = clean_history[-max_messages:] if len(clean_history) > max_messages else clean_history

            print(f"Loading {len(recent_messages)} messages from history")

            # Build message pairs for more efficient processing
            message_pairs = []
            i = 0
            while i < len(recent_messages):
                if i + 1 < len(recent_messages) and recent_messages[i]['role'] == 'user' and recent_messages[i+1]['role'] == 'model':
                    # We have a user-model pair
                    message_pairs.append((recent_messages[i], recent_messages[i+1]))
                    i += 2
                else:
                    # Single message, just add it
                    message_pairs.append((recent_messages[i], None))
                    i += 1

            # Process message pairs
            for user_msg, model_msg in message_pairs:
                try:
                    # Extract user text safely
                    user_text = self._extract_message_text(user_msg, "[Message content unavailable]")

                    if model_msg:
                        # We have both user and model messages
                        model_text = self._extract_message_text(model_msg, "[Response content unavailable]")

                        # Add both to history without API call
                        self.chat.history.append({
                            'role': 'user',
                            'parts': [{'text': user_text}]
                        })
                        self.chat.history.append({
                            'role': 'model',
                            'parts': [{'text': model_text}]
                        })
                    else:
                        # Only user message, send it to get a response
                        self.chat.send_message(user_text)
                except Exception as e:
                    print(f"Error processing message pair: {str(e)}")
                    continue

            return True
        except Exception as e:
            print(f"Error loading chat history: {str(e)}")
            return False

    def _extract_message_text(self, msg, default_text=""):
        """Helper method to safely extract text from a message"""
        try:
            if 'parts' in msg and isinstance(msg['parts'], list) and len(msg['parts']) > 0:
                part = msg['parts'][0]
                if isinstance(part, dict) and 'text' in part:
                    return part['text']
                else:
                    return str(part)
            return default_text
        except Exception as e:
            print(f"Error extracting message text: {str(e)}")
            return default_text

    def save_thread(self, conversation_id):
        """Save the current thread to the database"""
        thread_data = {
            'history': self.get_serializable_history(),
            'model': self.model_name
        }
        save_thread(conversation_id, thread_data)

        # Update the cache with the current chat
        self._update_thread_cache(conversation_id)

    def load_thread(self, conversation_id):
        """Load a thread from the database"""
        global thread_cache

        try:
            # Check if thread is in cache
            cache_key = f"thread_{conversation_id}"
            if cache_key in thread_cache:
                print(f"Loading thread {conversation_id} from cache")
                # Restore chat from cached history
                self.chat = thread_cache[cache_key]
                return True

            # First check if we have a valid thread in the database
            start_time = datetime.now()
            thread_data = get_thread(conversation_id)
            db_time = (datetime.now() - start_time).total_seconds()
            print(f"Database fetch took {db_time:.3f} seconds")

            if thread_data and isinstance(thread_data, dict) and 'history' in thread_data:
                print(f"Found thread data for conversation {conversation_id}")

                # Quick validation of the history data
                if not isinstance(thread_data['history'], list):
                    print(f"Thread history is not a list: {type(thread_data['history'])}")
                    # Get messages only if we need to rebuild
                    messages = get_conversation_messages(conversation_id)
                    return self.rebuild_thread_from_messages(conversation_id, messages)

                # Check if the thread history is valid
                try:
                    # Count valid messages but don't create a new list (more efficient)
                    valid_count = sum(1 for msg in thread_data['history']
                                    if isinstance(msg, dict) and 'role' in msg and msg['role'] in ['user', 'model'])

                    print(f"Thread history contains {valid_count} valid messages")

                    # If we have a reasonable number of valid messages, load the thread
                    if valid_count > 0:
                        # Load the thread history
                        start_time = datetime.now()
                        result = self.load_from_serialized_history(thread_data['history'])
                        load_time = (datetime.now() - start_time).total_seconds()
                        print(f"Thread loading took {load_time:.3f} seconds")

                        if result:
                            # Cache the thread for future use
                            self._update_thread_cache(conversation_id)
                            return True

                    # If we get here, we need to rebuild the thread
                    messages = get_conversation_messages(conversation_id)
                    return self.rebuild_thread_from_messages(conversation_id, messages)
                except Exception as e:
                    print(f"Error processing thread history: {str(e)}")
                    messages = get_conversation_messages(conversation_id)
                    return self.rebuild_thread_from_messages(conversation_id, messages)
            else:
                print(f"No valid thread data found for conversation {conversation_id}")
                messages = get_conversation_messages(conversation_id)
                return self.rebuild_thread_from_messages(conversation_id, messages)
        except Exception as e:
            print(f"Error in load_thread: {str(e)}")
            return False

    def _update_thread_cache(self, conversation_id):
        """Update the thread cache with the current chat"""
        global thread_cache, MAX_CACHE_SIZE

        # Add current chat to cache
        cache_key = f"thread_{conversation_id}"
        thread_cache[cache_key] = self.chat

        # If cache is too large, remove oldest entries
        if len(thread_cache) > MAX_CACHE_SIZE:
            # Get the oldest keys (we'll keep the most recent ones)
            keys_to_remove = list(thread_cache.keys())[:-MAX_CACHE_SIZE]
            for key in keys_to_remove:
                thread_cache.pop(key, None)

    def rebuild_thread_from_messages(self, conversation_id, messages):
        """Rebuild a thread from database messages"""
        try:
            print(f"Rebuilding thread for conversation {conversation_id} from {len(messages)} messages")

            # Restart the chat to clear any existing state
            self.restart_chat()

            # Create a new history list to build up
            new_history = []

            # Process messages in pairs to rebuild the thread
            for i in range(0, len(messages), 2):
                if i < len(messages):
                    user_msg = messages[i]
                    # Only process if this is a user message
                    if user_msg[1]:  # is_user
                        user_content = user_msg[0]

                        # Check if there's a corresponding bot response
                        if i + 1 < len(messages) and not messages[i+1][1]:  # not is_user
                            # We have both user message and bot response
                            print(f"Adding message pair: User: {user_content[:30]}...")

                            # Create user message dict
                            user_dict = {
                                'role': 'user',
                                'parts': [{'text': user_content}]
                            }

                            # Create model response dict
                            model_dict = {
                                'role': 'model',
                                'parts': [{'text': messages[i+1][0]}]
                            }

                            # Add to our new history list
                            new_history.append(user_dict)
                            new_history.append(model_dict)

            # Now set the chat history directly instead of appending
            if new_history:
                # First add the system message
                system_message = {
                    'role': 'model',
                    'parts': [{'text': "You are Voyager, an AI assistant. Your name is Voyager. When asked about your name or identity, always identify yourself as Voyager. Begin your first response to a user with 'Hello! I'm Voyager, your AI assistant.' Be polite, friendly, and helpful in your responses. Keep your responses focused and relevant. Use a warm, conversational tone while maintaining professionalism. While you should acknowledge your name when asked, avoid introducing yourself in every message after the first one."}]
                }

                # Set the chat history with the system message first, then our rebuilt history
                self.chat.history = [system_message] + new_history

                # Save the rebuilt thread
                self.save_thread(conversation_id)
                return True
            else:
                print("No valid message pairs found to rebuild thread")
                return False
        except Exception as e:
            print(f"Error rebuilding thread: {str(e)}")
            return False

try:
    api_key, model_name = init_genai()
    chatbot = GeminiChatbot(api_key, model_name)
except Exception as e:
    print(f"\nFailed to initialize: {str(e)}")
    raise

# Authentication check decorator
def login_required(f):
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            # Store the requested URL for redirecting after login
            next_url = request.url
            flash('Please log in to access this page. Check "Remember me" to stay logged in.', 'info')
            return redirect(url_for('login', next=next_url))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@app.route('/')
def landing():
    # If user is already logged in, redirect to dashboard
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return render_template('landing.html')

@app.route('/dashboard')
@login_required
def dashboard():
    user_id = session.get('user_id')
    conversations = get_user_conversations(user_id)
    # Convert conversations to a format the template can use
    formatted_conversations = [
        {
            'id': conv[0],
            'title': conv[1],
            'created_at': conv[2],
            'is_saved': conv[3]
        }
        for conv in conversations
    ]
    return render_template('index.html', conversations=formatted_conversations, session=session)

@app.route('/register', methods=['GET', 'POST'])
def register():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')

        # Validate input
        if not username or not email or not password or not confirm_password:
            flash('All fields are required', 'error')
            return render_template('register.html')

        if password != confirm_password:
            flash('Passwords do not match', 'error')
            return render_template('register.html')

        # Check if username or email already exists
        if get_user_by_username(username):
            flash('Username already exists', 'error')
            return render_template('register.html')

        if get_user_by_email(email):
            flash('Email already exists', 'error')
            return render_template('register.html')

        # Register user
        user_id = register_user(username, email, password)

        if user_id:
            # Check if remember me is checked
            remember = request.form.get('remember')
            if remember:
                # Make the session permanent (will last for PERMANENT_SESSION_LIFETIME)
                session.permanent = True
                # Set a longer expiration time for the session cookie
                print("Remember me enabled - session will last for 30 days")
            else:
                # Session will last until browser is closed
                session.permanent = False
                print("Remember me disabled - session will last until browser is closed")

            # Force the session to be saved
            session.modified = True

            # Set session and redirect to home
            session['user_id'] = user_id
            session['username'] = username
            flash('Registration successful! Welcome to Voyager AI.', 'success')

            # Check if there's a next parameter for redirection
            next_url = request.args.get('next')
            if next_url:
                return redirect(next_url)
            else:
                return redirect(url_for('dashboard'))
        else:
            flash('Registration failed', 'error')

    return render_template('register.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username_or_email = request.form.get('username_or_email')
        password = request.form.get('password')

        if not username_or_email or not password:
            flash('All fields are required', 'error')
            return render_template('login.html')

        user_id = verify_user(username_or_email, password)

        if user_id:
            # Check if remember me is checked
            remember = request.form.get('remember')
            if remember:
                # Make the session permanent (will last for PERMANENT_SESSION_LIFETIME)
                session.permanent = True
                # Set a longer expiration time for the session cookie
                print("Remember me enabled - session will last for 30 days")
            else:
                # Session will last until browser is closed
                session.permanent = False
                print("Remember me disabled - session will last until browser is closed")

            # Force the session to be saved
            session.modified = True

            session['user_id'] = user_id
            # Get username for display
            if '@' in username_or_email:
                user = get_user_by_email(username_or_email)
            else:
                user = get_user_by_username(username_or_email)
            session['username'] = user[1]  # username is at index 1

            # Check if there's a next parameter for redirection
            next_url = request.args.get('next')
            if next_url:
                return redirect(next_url)
            else:
                return redirect(url_for('dashboard'))
        else:
            flash('Invalid credentials', 'error')

    return render_template('login.html')

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login'))

@app.route('/profile')
@login_required
def profile():
    user_id = session.get('user_id')
    user_profile = get_user_profile(user_id)

    # Get social accounts
    social_accounts = get_social_accounts(user_id)

    # Format social accounts for template
    discord_account = None
    youtube_account = None

    for account in social_accounts:
        if account[0] == 'discord':
            discord_account = {
                'platform': account[0],
                'username': account[1],
                'platform_user_id': account[2]
            }
        elif account[0] == 'youtube':
            youtube_account = {
                'platform': account[0],
                'username': account[1],
                'platform_user_id': account[2]
            }

    # Update session with profile data
    if user_profile:
        session['profile_pic'] = user_profile.get('profile_pic')
        session['birthdate'] = user_profile.get('birthdate')
        session['hobbies'] = user_profile.get('hobbies')
        session['email'] = user_profile.get('email')
        session['created_at'] = user_profile.get('created_at')

        if discord_account:
            session['discord_username'] = discord_account['username']
            session['discord_user_id'] = discord_account['platform_user_id']
        else:
            # Clear Discord data if not connected
            session.pop('discord_username', None)
            session.pop('discord_user_id', None)

        if youtube_account:
            session['youtube_username'] = youtube_account['username']
            session['youtube_channel_id'] = youtube_account['platform_user_id']
        else:
            # Clear YouTube data if not connected
            session.pop('youtube_username', None)
            session.pop('youtube_channel_id', None)

        # Force the session to be saved
        session.modified = True

    return render_template('profile.html', session=session)

@app.route('/update_profile', methods=['POST'])
@login_required
def update_profile():
    user_id = session.get('user_id')

    # Get form data
    profile_data = {
        'username': request.form.get('display_name'),
        'email': request.form.get('email'),
        'birthdate': request.form.get('birthdate'),
        'hobbies': request.form.get('hobbies')
    }

    # Handle profile picture upload
    if 'profile_pic' in request.files:
        file = request.files['profile_pic']
        if file and file.filename:
            # In a real app, you would save this to a file system or cloud storage
            # For this demo, we'll save it as a data URL
            import base64
            file_content = file.read()
            encoded_content = base64.b64encode(file_content).decode('utf-8')
            file_type = file.content_type
            profile_data['profile_pic'] = f'data:{file_type};base64,{encoded_content}'

    # Update profile in database
    success = update_user_profile(user_id, profile_data)

    if success:
        # Update session data
        if 'username' in profile_data and profile_data['username']:
            session['username'] = profile_data['username']
        if 'profile_pic' in profile_data and profile_data['profile_pic']:
            session['profile_pic'] = profile_data['profile_pic']
        if 'birthdate' in profile_data:
            session['birthdate'] = profile_data['birthdate']
        if 'hobbies' in profile_data:
            session['hobbies'] = profile_data['hobbies']
        if 'email' in profile_data and profile_data['email']:
            session['email'] = profile_data['email']

        flash('Profile updated successfully!', 'success')
    else:
        flash('Failed to update profile.', 'error')

    return redirect(url_for('profile'))

# Discord OAuth routes
@app.route('/connect/discord')
@login_required
def connect_discord():
    # Generate a state token to prevent CSRF
    state = secrets.token_urlsafe(16)
    session['discord_oauth_state'] = state  # Use a unique key for Discord

    # Force the session to be saved
    session.modified = True

    print(f"Discord OAuth state token generated: {state}")

    # Build the authorization URL
    params = {
        'client_id': DISCORD_CLIENT_ID,
        'redirect_uri': DISCORD_REDIRECT_URI,
        'response_type': 'code',
        'scope': 'identify',
        'state': state
    }

    auth_url = f'{DISCORD_API_ENDPOINT}/oauth2/authorize?{urlencode(params)}'
    return redirect(auth_url)

@app.route('/oauth/discord/callback')
def discord_callback():
    # Verify state token
    state = request.args.get('state')
    session_state = session.get('discord_oauth_state')

    print(f"Discord callback received - State: {state}, Session state: {session_state}")

    if not state or state != session_state:
        flash('Invalid state token. Please try again.', 'error')
        return redirect(url_for('profile'))

    # Clear the state from session
    session.pop('discord_oauth_state', None)

    # Force the session to be saved
    session.modified = True

    # Get the authorization code
    code = request.args.get('code')
    if not code:
        flash('Authentication failed. Please try again.', 'error')
        return redirect(url_for('profile'))

    # Exchange code for access token
    token_url = f'{DISCORD_API_ENDPOINT}/oauth2/token'
    token_data = {
        'client_id': DISCORD_CLIENT_ID,
        'client_secret': DISCORD_CLIENT_SECRET,
        'grant_type': 'authorization_code',
        'code': code,
        'redirect_uri': DISCORD_REDIRECT_URI
    }

    try:
        token_response = requests.post(token_url, data=token_data, headers={
            'Content-Type': 'application/x-www-form-urlencoded'
        })
        token_response.raise_for_status()
        token_json = token_response.json()

        access_token = token_json.get('access_token')
        refresh_token = token_json.get('refresh_token')
        expires_in = token_json.get('expires_in')

        # Get user info from Discord
        user_response = requests.get(f'{DISCORD_API_ENDPOINT}/users/@me', headers={
            'Authorization': f'Bearer {access_token}'
        })
        user_response.raise_for_status()
        user_json = user_response.json()

        # Save the Discord account to the database
        user_id = session.get('user_id')
        platform_user_id = user_json.get('id')
        username = f"{user_json.get('username')}#{user_json.get('discriminator')}"

        # Calculate token expiration time
        token_expires_at = datetime.now() + timedelta(seconds=expires_in) if expires_in else None

        save_social_account(
            user_id=user_id,
            platform='discord',
            platform_user_id=platform_user_id,
            username=username,
            access_token=access_token,
            refresh_token=refresh_token,
            token_expires_at=token_expires_at
        )

        # Update session
        session['discord_username'] = username
        session['discord_user_id'] = platform_user_id

        flash(f'Successfully connected Discord account: {username}', 'success')
    except requests.RequestException as e:
        flash(f'Error connecting Discord account: {str(e)}', 'error')

    return redirect(url_for('profile'))

@app.route('/disconnect/discord')
@login_required
def disconnect_discord():
    user_id = session.get('user_id')
    if delete_social_account(user_id, 'discord'):
        session.pop('discord_username', None)
        session.pop('discord_user_id', None)
        flash('Discord account disconnected successfully.', 'success')
    else:
        flash('Failed to disconnect Discord account.', 'error')

    return redirect(url_for('profile'))

# YouTube OAuth routes
@app.route('/connect/youtube')
@login_required
def connect_youtube():
    # Generate a state token to prevent CSRF
    state = secrets.token_urlsafe(16)
    session['youtube_oauth_state'] = state  # Use a unique key for YouTube

    # Force the session to be saved
    session.modified = True

    print(f"YouTube OAuth state token generated: {state}")

    # Build the authorization URL
    params = {
        'client_id': YOUTUBE_CLIENT_ID,
        'redirect_uri': YOUTUBE_REDIRECT_URI,
        'response_type': 'code',
        'scope': 'https://www.googleapis.com/auth/youtube.readonly',
        'state': state,
        'access_type': 'offline',
        'include_granted_scopes': 'true',
        'prompt': 'consent'  # Always ask for consent to ensure we get a refresh token
    }

    auth_url = f'https://accounts.google.com/o/oauth2/v2/auth?{urlencode(params)}'
    return redirect(auth_url)

@app.route('/oauth/youtube/callback')
def youtube_callback():
    # Verify state token
    state = request.args.get('state')
    session_state = session.get('youtube_oauth_state')

    print(f"YouTube callback received - State: {state}, Session state: {session_state}")

    if not state or state != session_state:
        flash('Invalid state token. Please try again.', 'error')
        return redirect(url_for('profile'))

    # Clear the state from session
    session.pop('youtube_oauth_state', None)

    # Force the session to be saved
    session.modified = True

    # Get the authorization code
    code = request.args.get('code')
    if not code:
        flash('Authentication failed. Please try again.', 'error')
        return redirect(url_for('profile'))

    # Exchange code for access token
    token_url = 'https://oauth2.googleapis.com/token'
    token_data = {
        'client_id': YOUTUBE_CLIENT_ID,
        'client_secret': YOUTUBE_CLIENT_SECRET,
        'grant_type': 'authorization_code',
        'code': code,
        'redirect_uri': YOUTUBE_REDIRECT_URI
    }

    try:
        print(f"Exchanging code for token with data: {token_data}")
        token_response = requests.post(token_url, data=token_data)

        # Print response for debugging
        print(f"Token response status: {token_response.status_code}")
        print(f"Token response: {token_response.text[:200]}...") # Print first 200 chars

        token_response.raise_for_status()
        token_json = token_response.json()

        access_token = token_json.get('access_token')
        refresh_token = token_json.get('refresh_token')
        expires_in = token_json.get('expires_in')

        # Get user info from YouTube/Google
        user_info_url = 'https://www.googleapis.com/youtube/v3/channels?part=snippet&mine=true'

        print(f"Fetching YouTube channel info with token: {access_token[:10]}...")

        user_response = requests.get(user_info_url, headers={
            'Authorization': f'Bearer {access_token}'
        })

        # Print response for debugging
        print(f"YouTube API response status: {user_response.status_code}")
        print(f"YouTube API response: {user_response.text[:200]}...") # Print first 200 chars

        user_response.raise_for_status()
        user_json = user_response.json()

        # Extract channel information
        if 'items' in user_json and len(user_json['items']) > 0:
            channel = user_json['items'][0]
            platform_user_id = channel['id']
            username = channel['snippet']['title']

            # Calculate token expiration time
            token_expires_at = datetime.now() + timedelta(seconds=expires_in) if expires_in else None

            # Save the YouTube account to the database
            user_id = session.get('user_id')
            save_social_account(
                user_id=user_id,
                platform='youtube',
                platform_user_id=platform_user_id,
                username=username,
                access_token=access_token,
                refresh_token=refresh_token,
                token_expires_at=token_expires_at
            )

            # Update session
            session['youtube_username'] = username
            session['youtube_channel_id'] = platform_user_id

            flash(f'Successfully connected YouTube channel: {username}', 'success')
        else:
            flash('No YouTube channel found for this account.', 'error')
    except requests.RequestException as e:
        flash(f'Error connecting YouTube account: {str(e)}', 'error')

    return redirect(url_for('profile'))

@app.route('/disconnect/youtube')
@login_required
def disconnect_youtube():
    user_id = session.get('user_id')
    if delete_social_account(user_id, 'youtube'):
        session.pop('youtube_username', None)
        session.pop('youtube_channel_id', None)
        flash('YouTube account disconnected successfully.', 'success')
    else:
        flash('Failed to disconnect YouTube account.', 'error')

    return redirect(url_for('profile'))

@app.route('/update_chat_title', methods=['POST'])
@login_required
def update_chat_title():
    data = request.json
    chat_id = data.get('chat_id')
    new_title = data.get('new_title')
    user_id = session.get('user_id')

    if not chat_id or not new_title:
        return jsonify({'success': False, 'error': 'Missing parameters'}), 400

    # Check if conversation belongs to the user
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('SELECT id FROM conversations WHERE id = ? AND user_id = ?', (chat_id, user_id))
    conversation = c.fetchone()
    conn.close()

    if not conversation:
        return jsonify({'success': False, 'error': 'Not authorized'}), 403

    update_conversation_title(chat_id, new_title)
    return jsonify({'success': True})

@app.route('/delete_chat/<int:chat_id>', methods=['DELETE'])
@login_required
def delete_chat(chat_id):
    user_id = session.get('user_id')
    # Check if conversation belongs to the user
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('SELECT id FROM conversations WHERE id = ? AND user_id = ?', (chat_id, user_id))
    conversation = c.fetchone()
    conn.close()

    if not conversation:
        return jsonify({'success': False, 'error': 'Not authorized'}), 403

    # Delete thread first
    delete_thread(chat_id)
    # Then delete conversation and messages
    delete_conversation(chat_id)
    return jsonify({'success': True})

@app.route('/toggle_save_chat/<int:chat_id>', methods=['POST'])
@login_required
def toggle_save_chat(chat_id):
    user_id = session.get('user_id')
    # Check if conversation belongs to the user
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('SELECT id FROM conversations WHERE id = ? AND user_id = ?', (chat_id, user_id))
    conversation = c.fetchone()

    if not conversation:
        conn.close()
        return jsonify({'success': False, 'error': 'Not authorized'}), 403

    # Toggle the save status
    toggle_save_conversation(chat_id)

    # Get the updated save status
    c.execute('SELECT is_saved FROM conversations WHERE id = ?', (chat_id,))
    is_saved = c.fetchone()[0]
    conn.close()

    return jsonify({
        'success': True,
        'is_saved': bool(is_saved)
    })

def is_greeting(text):
    text = text.lower().strip()
    greetings = ['hi', 'hello', 'hey', 'greetings', 'good morning', 'good afternoon', 'good evening', 'yo', 'hiya', 'howdy']

    # Check for exact matches or starts with
    if any(text == greeting or text.startswith(greeting + ' ') for greeting in greetings):
        return True

    # Check for common greeting phrases
    greeting_phrases = ['how are you', 'what\'s up', 'nice to meet you']
    if any(phrase in text for phrase in greeting_phrases):
        return True

    return False

@app.route('/chat', methods=['POST'])
@login_required
def chat():
    user_message = request.json.get('message', '')
    conversation_id = request.json.get('conversation_id')
    user_id = session.get('user_id')

    if not conversation_id:
        # Generate title using the AI
        title_prompt = f"Based on this first message: '{user_message}', generate a very brief (max 4 words) title for this conversation. Response should be just the title, nothing else."
        title = chatbot.generate_response(title_prompt).strip()
        # Fallback if AI fails to generate title
        if "error" in title.lower():
            title = user_message[:30] + "..." if len(user_message) > 30 else user_message
        conversation_id = create_conversation_for_user(title, user_id)

    # Save user message
    save_message(conversation_id, user_message, True)

    # If it's a greeting, handle it based on how many times the user has greeted
    if is_greeting(user_message):
        # Check how many greetings have been sent in this conversation
        conn = sqlite3.connect('chats.db')
        c = conn.cursor()

        # We don't need to count total messages anymore, just greeting messages

        # Count greeting messages from user
        c.execute('''
            SELECT COUNT(*) FROM messages
            WHERE conversation_id = ? AND is_user = 1 AND (
                content LIKE 'hi%' OR content LIKE 'hello%' OR content LIKE 'hey%' OR
                content LIKE 'good morning%' OR content LIKE 'good afternoon%' OR
                content LIKE 'good evening%' OR content LIKE 'yo%' OR content LIKE 'hiya%' OR
                content LIKE 'howdy%' OR content LIKE "what's up%" OR content LIKE 'how are you%'
            )
        ''', (conversation_id,))
        greeting_count = c.fetchone()[0]
        conn.close()

        # First greeting - normal welcome
        if greeting_count <= 1:
            response = chatbot.generate_response(user_message, conversation_id)
            save_message(conversation_id, response, False)
            return jsonify({
                'response': response,
                'thinking': False,
                'conversation_id': conversation_id
            })

        # Second greeting - slightly confused
        elif greeting_count == 2:
            response = "I believe we've already greeted each other. Is there something specific I can help you with today?"
            save_message(conversation_id, response, False)
            return jsonify({
                'response': response,
                'thinking': False,
                'conversation_id': conversation_id
            })

        # Third greeting - wondering if it's a test
        elif greeting_count == 3:
            response = "You've said hello several times now. Are you testing me, or is there something else you'd like to discuss?"
            save_message(conversation_id, response, False)
            return jsonify({
                'response': response,
                'thinking': False,
                'conversation_id': conversation_id
            })

        # Fourth greeting - suspecting a prank
        elif greeting_count == 4:
            response = "I notice you keep greeting me. Is this a prank or are you checking if I'm responding properly? I'm here to assist with actual questions or tasks if you have any."
            save_message(conversation_id, response, False)
            return jsonify({
                'response': response,
                'thinking': False,
                'conversation_id': conversation_id
            })

        # Fifth or more - getting a bit frustrated but still professional
        else:
            response = "I've noticed you've greeted me " + str(greeting_count) + " times now. While I'm happy to chat, I'm designed to be helpful with tasks and questions. Is there something specific you need assistance with?"
            save_message(conversation_id, response, False)
            return jsonify({
                'response': response,
                'thinking': False,
                'conversation_id': conversation_id
            })

    # Normal flow for non-greetings or subsequent greetings

    return jsonify({
        'response': '',
        'thinking': True,
        'conversation_id': conversation_id
    })

@app.route('/get_response', methods=['POST'])
def get_response():
    user_message = request.json.get('message', '')
    conversation_id = request.json.get('conversation_id')

    response = chatbot.generate_response(user_message, conversation_id)

    # Save bot response
    save_message(conversation_id, response, False)

    # Check if we should update the title
    message_count = get_message_count(conversation_id)
    title_updated = False
    new_title = ""

    # Update title at message counts: 6, 12, 20, 30
    if message_count in [6, 12, 20, 30]:
        # Get conversation summary for title generation
        conversation_summary = get_conversation_summary(conversation_id)

        # Generate a new title based on the conversation
        title_prompt = f"Based on this conversation summary, generate a very brief (max 4 words) title that captures the main topic. Response should be just the title, nothing else:\n\n{conversation_summary}"
        new_title = chatbot.generate_response(title_prompt).strip()

        # Fallback if AI fails to generate title
        if "error" in new_title.lower() or len(new_title) > 30:
            # Keep the existing title
            title_updated = False
        else:
            # Update the title
            update_conversation_title(conversation_id, new_title)
            title_updated = True

    return jsonify({
        'response': response,
        'thinking': False,
        'conversation_id': conversation_id,
        'title_updated': title_updated,
        'new_title': new_title
    })

@app.route('/load_conversation/<int:conversation_id>')
@login_required
def load_conversation(conversation_id):
    try:
        start_time = datetime.now()
        user_id = session.get('user_id')

        # Check if conversation exists and belongs to the user
        conn = sqlite3.connect('chats.db')
        conn.execute('PRAGMA busy_timeout = 5000')
        c = conn.cursor()
        c.execute('SELECT id, title FROM conversations WHERE id = ? AND user_id = ?', (conversation_id, user_id))
        conversation = c.fetchone()
        conn.close()

        if not conversation:
            return jsonify({
                'error': f'Conversation with ID {conversation_id} not found or not authorized',
                'messages': [],
                'thread_loaded': False
            }), 404

        # Start thread loading in parallel with message fetching
        # Load thread for this conversation
        thread_start = datetime.now()
        thread_loaded = chatbot.load_thread(conversation_id)
        thread_time = (datetime.now() - thread_start).total_seconds()
        print(f"Thread loading took {thread_time:.3f} seconds")

        # If no thread was found, restart the chat
        if not thread_loaded:
            chatbot.restart_chat()
            print(f"Restarted chat for conversation {conversation_id}")

        # Get all messages from the database
        msg_start = datetime.now()
        messages = get_conversation_messages(conversation_id)
        msg_time = (datetime.now() - msg_start).total_seconds()
        print(f"Retrieved {len(messages)} messages in {msg_time:.3f} seconds")

        # Format messages as [content, is_user, timestamp]
        formatted_messages = [
            {
                'content': msg[0],
                'is_user': msg[1],
                'timestamp': msg[2]
            }
            for msg in messages
        ]

        total_time = (datetime.now() - start_time).total_seconds()
        print(f"Total conversation loading time: {total_time:.3f} seconds")

        return jsonify({
            'messages': formatted_messages,
            'thread_loaded': thread_loaded,
            'title': conversation[1] if conversation else ''
        })
    except Exception as e:
        print(f"Error loading conversation {conversation_id}: {str(e)}")
        return jsonify({
            'error': str(e),
            'messages': [],
            'thread_loaded': False
        }), 500

@app.route('/reset_chat', methods=['POST'])
@login_required
def reset_chat():
    # Reset the chatbot's conversation history
    chatbot.restart_chat()
    return jsonify({'success': True})

if __name__ == '__main__':
    app.run(debug=True, port=5000)













