<!DOCTYPE html>
<html>
<head>
    <title>Voyager AI</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            /* Dark theme (default) */
            --bg-color: #1a1a1a;
            --text-color: #ffffff;
            --sidebar-bg: #262626;
            --sidebar-border: #333;
            --container-bg: #2d2d2d;
            --input-bg: #404040;
            --input-container-bg: #262626;
            --user-message-bg: #404040;
            --bot-message-bg: #333333;
            --bot-message-text: #e0e0e0;
            --chat-item-bg: #333;
            --chat-item-hover: #404040;
            --user-info-bg: #333;
            --button-bg: #404040;
            --button-hover: #505050;
            --scrollbar-track: #2d2d2d;
            --scrollbar-thumb: #666666;
            --scrollbar-thumb-hover: #808080;
            --modal-bg: #2c2c2c;
            --active-chat-bg: #4a4a4a;
            --active-chat-border: #007bff;
            --chat-menu-bg: #404040;
            --chat-menu-hover: #505050;
            --notification-success: #28a745;
            --notification-error: #dc3545;
            --notification-info: #17a2b8;
            --delete-btn: #dc3545;
            --cancel-btn: #6c757d;
            --saved-bg: #2c3e50;
            --star-color: #ffd700;
            --transition-speed: 0.3s;
        }

        /* Light theme (gray and white) */
        :root.light-theme {
            --bg-color: #f5f5f5;
            --text-color: #333333;
            --sidebar-bg: #e0e0e0; /* Gray for sidebar */
            --sidebar-border: #bdbdbd;
            --container-bg: #ffffff;
            --input-bg: #f0f0f0;
            --input-container-bg: #e0e0e0; /* Gray for input container */
            --user-message-bg: #e0e0e0;
            --bot-message-bg: #f0f0f0;
            --bot-message-text: #333333;
            --chat-item-bg: #e0e0e0;
            --chat-item-hover: #bdbdbd;
            --user-info-bg: #bdbdbd; /* Darker gray for user info */
            --button-bg: #e0e0e0;
            --button-hover: #bdbdbd;
            --scrollbar-track: #f5f5f5;
            --scrollbar-thumb: #e0e0e0;
            --scrollbar-thumb-hover: #bdbdbd;
            --modal-bg: #f5f5f5;
            --active-chat-bg: #bdbdbd; /* Darker gray for active chat */
            --active-chat-border: #757575;
            --chat-menu-bg: #f5f5f5;
            --chat-menu-hover: #e0e0e0;
            --notification-success: #66bb6a;
            --notification-error: #ef5350;
            --notification-info: #42a5f5;
            --delete-btn: #ef5350;
            --cancel-btn: #9e9e9e;
            --saved-bg: #9e9e9e; /* Darker gray for saved chats */
            --star-color: #ffc107;
            --transition-speed: 0.3s;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            transition: background-color var(--transition-speed) ease, color var(--transition-speed) ease;
        }

        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            height: 100vh;
            display: flex;
            padding: 20px;
        }



        .container {
            display: flex;
            width: 100%;
            height: 100vh;
            overflow: hidden; /* Prevent overflow from affecting layout */
            gap: 10px; /* Add spacing between sidebar and main content */
        }

        .sidebar {
            width: 250px;
            min-width: 250px; /* Prevent sidebar from shrinking */
            flex-shrink: 0; /* Prevent sidebar from shrinking */
            background-color: var(--sidebar-bg);
            padding: 20px;
            overflow-y: auto;
            overflow-x: hidden; /* Prevent horizontal scrollbar */
            transition: width 0.3s ease, min-width 0.3s ease, background-color var(--transition-speed) ease;
            position: relative;
            border-radius: 15px;
            margin-right: 10px;
            z-index: 10; /* Lower z-index than dropdown menus */
        }

        .sidebar.collapsed {
            width: 60px;
            min-width: 60px; /* Prevent collapsed sidebar from shrinking */
            padding: 20px 10px;
            border-radius: 15px;
        }

        .sidebar.collapsed #current-chat-title {
            display: none;
        }

        .sidebar.collapsed .chat-list {
            margin-top: 10px;
        }

        .sidebar.collapsed .chat-item:not(.saved) {
            display: none;
        }

        .sidebar.collapsed .chat-item.saved .chat-title-text {
            max-width: 40px;
            font-size: 0.8em;
            text-align: center;
        }

        .sidebar.collapsed .chat-item.saved .chat-title-text .chat-title {
            display: none;
        }

        /* Fix for saved chats in collapsed sidebar */
        .sidebar.collapsed .chat-item.saved {
            display: flex !important;
            justify-content: center;
            align-items: center;
            padding: 8px 5px;
        }

        /* Make saved chats more visible in collapsed sidebar */
        .sidebar.collapsed .chat-item.saved .chat-title-text {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .sidebar.collapsed .chat-item.saved .fa-star {
            font-size: 14px;
            margin-bottom: 5px;
        }

        .chat-number {
            display: inline-block;
            margin: 0 5px;
            font-weight: bold;
        }

        /* Make chat numbers more visible in collapsed sidebar */
        .sidebar.collapsed .chat-number {
            font-size: 16px;
            display: block;
            text-align: center;
            margin: 0 auto;
            color: var(--accent-color);
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            width: 24px;
            height: 24px;
            line-height: 24px;
        }

        .sidebar.collapsed .chat-actions {
            display: none;
        }

        .sidebar-toggle {
            position: absolute;
            top: 50%;
            right: 0;
            transform: translateY(-50%);
            background-color: var(--sidebar-bg);
            border: 1px solid var(--sidebar-border);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-color);
            cursor: pointer;
            font-size: 14px;
            z-index: 10;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            opacity: 0;
            transition: opacity 0.3s ease, background-color 0.3s ease;
        }

        .sidebar:hover .sidebar-toggle {
            opacity: 1;
        }

        .sidebar-toggle:hover {
            color: var(--text-color);
            background-color: var(--button-hover);
        }

        .chat-list {
            list-style: none;
            padding: 0;
            margin-top: 20px;
            max-height: calc(100vh - 150px);
            overflow-y: auto;
        }

        .chat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 10px;
            margin: 3px 0;
            background-color: var(--chat-item-bg);
            border-radius: 8px;
            cursor: pointer;
            transition: background-color var(--transition-speed);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .chat-item-content {
            flex-grow: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .chat-item:hover {
            background-color: var(--chat-item-hover);
        }

        .chat-title-text {
            flex-grow: 1;
            margin-right: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 180px;
            font-size: 0.9em;
        }

        .chat-actions {
            position: relative;
            display: flex;
            align-items: center;
            z-index: 10;
            opacity: 0.7;
            transition: opacity 0.2s ease;
        }

        .chat-item:hover .chat-actions {
            opacity: 1;
        }

        .menu-button {
            background-color: #555555; /* Darker background for better visibility */
            border: none;
            color: #ffffff;
            cursor: pointer;
            padding: 8px 10px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            z-index: 10;
        }

        .menu-button:hover {
            background-color: #666666;
            transform: scale(1.05);
            box-shadow: 0 3px 6px rgba(0,0,0,0.3);
        }

        .menu-button:active {
            transform: scale(0.95);
            background-color: #777777;
        }

        .menu-button i {
            font-size: 16px;
            color: #ffffff;
        }

        .chat-actions i {
            padding: 5px;
            color: #888;
        }

        .chat-actions i:hover {
            color: #fff;
        }

        /* Dropdown menu styling */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 100%;
            background-color: var(--chat-menu-bg);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            z-index: 1000;
            min-width: 150px;
            margin-top: 5px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            overflow: hidden;
        }

        .dropdown-content a {
            color: var(--text-color);
            padding: 10px 15px;
            text-decoration: none;
            display: block;
            transition: background-color 0.2s;
            user-select: none;
        }

        .dropdown-content a:hover {
            background-color: var(--chat-menu-hover);
        }

        .dropdown-content a i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
        }

        .dropdown-content.show {
            display: block;
            animation: fadeInDropdown 0.2s ease-out;
        }

        @keyframes fadeInDropdown {
            from { opacity: 0; transform: translateY(-5px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .new-chat-btn {
            width: 100%;
            padding: 10px;
            background-color: var(--button-bg);
            border: none;
            border-radius: 8px;
            color: var(--text-color);
            cursor: pointer;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .new-chat-btn span {
            margin-left: 5px;
        }

        .sidebar.collapsed .new-chat-btn span {
            display: none;
        }

        .new-chat-btn:hover {
            background-color: var(--button-hover);
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-width: 0; /* Allow content to shrink below its minimum content size */
            overflow: hidden; /* Prevent overflow from affecting layout */
            border-left: none; /* Ensure no border on the left side */
            padding: 0 5px; /* Add a little padding for cleaner look */
        }

        .user-info {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 15px 10px;
            background-color: transparent;
            border-radius: 12px;
            margin-bottom: 15px;
            transition: all var(--transition-speed) ease;
        }

        .user-info:hover {
            background-color: var(--button-hover);
            cursor: pointer;
        }

        .username {
            font-weight: 600;
            color: var(--text-color);
            display: flex;
            align-items: center;
            font-size: 14px;
        }

        .profile-pic {
            width: 38px;
            height: 38px;
            border-radius: 50%;
            background-color: var(--button-bg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            overflow: hidden;
            box-shadow: 0 2px 6px rgba(0,0,0,0.15);
            border: 2px solid var(--sidebar-border);
            transition: transform 0.2s ease;
        }

        .user-info:hover .profile-pic {
            transform: scale(1.05);
        }

        .profile-pic img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-pic .initial-avatar {
            font-weight: bold;
            color: var(--text-color);
            font-size: 16px;
        }

        .sidebar.collapsed .username span.full-name {
            display: none;
        }

        .sidebar.collapsed .username .profile-pic {
            margin-right: 0;
            margin: 0 auto;
        }



        .main-container {
            display: flex;
            flex-direction: column;
            height: 100%;
            max-width: 1000px;
            margin: 0 auto;
            width: 100%;
            gap: 15px;
        }

        #chat-container {
            flex-grow: 1;
            overflow-y: auto;
            overflow-x: hidden; /* Prevent horizontal scrolling */
            padding: 20px;
            margin-bottom: 0;
            background-color: var(--container-bg);
            border-radius: 25px;
            width: 100%; /* Ensure it takes full width */
        }

        .message-container {
            width: 100%;
            display: flex;
            margin-bottom: 15px;
        }

        .message-container.user-container {
            justify-content: flex-end;
        }

        .message-container.bot-container {
            justify-content: flex-start;
        }

        .message {
            padding: 15px;
            border-radius: 15px;
            line-height: 1.4;
            word-wrap: break-word;
            word-break: break-word; /* Break long words if needed */
            max-width: 80%;
            min-width: 0; /* Allow content to shrink */
            opacity: 0;
            transform: translateY(20px);
            animation: fadeIn 0.3s ease forwards;
            overflow-wrap: break-word; /* Ensure long words don't overflow */
        }

        @keyframes fadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .user-message {
            background-color: var(--user-message-bg);
            color: var(--text-color);
        }

        .bot-message {
            background-color: var(--bot-message-bg);
            color: var(--bot-message-text);
        }

        #input-container {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 20px;
            background-color: var(--input-container-bg);
            border-radius: 25px;
        }

        #user-input {
            flex-grow: 1;
            padding: 15px;
            border: none;
            border-radius: 12px;
            background-color: var(--input-bg);
            color: var(--text-color);
            font-size: 16px;
        }

        #user-input:focus {
            outline: none;
            box-shadow: 0 0 0 2px #666666;
        }

        #user-input::placeholder {
            color: #888888;
        }

        #send-button {
            background: none;
            border: none;
            color: var(--text-color);
            opacity: 0.7;
            cursor: pointer;
            font-size: 24px;
            padding: 10px;
            transition: opacity var(--transition-speed) ease;
        }

        #send-button:hover {
            opacity: 1;
        }

        /* Code block styling */
        pre {
            background-color: #282c34;
            border-radius: 5px;
            padding: 10px;
            overflow-x: auto;
            margin: 10px 0;
            position: relative;
        }

        code {
            font-family: 'Courier New', Courier, monospace;
            color: #e6e6e6;
        }

        /* Line-by-line code display */
        .code-container {
            position: relative;
            background-color: #282c34;
            border-radius: 5px;
            margin: 15px 0;
            overflow: hidden;
        }

        .code-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 15px;
            background-color: rgba(0, 0, 0, 0.2);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .code-language {
            font-size: 12px;
            font-weight: bold;
            color: #aaaaaa;
        }

        .copy-button {
            background-color: #4d4d4d;
            color: #ffffff;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .copy-button:hover {
            background-color: #666666;
        }

        .code-content {
            padding: 0;
            margin: 0;
            list-style-type: none;
            counter-reset: line;
        }

        .code-line {
            display: flex;
            padding: 0 15px;
            line-height: 1.5;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }

        .code-line:last-child {
            border-bottom: none;
        }

        .code-line:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .line-number {
            counter-increment: line;
            width: 40px;
            text-align: right;
            color: #aaaaaa;
            padding-right: 15px;
            user-select: none;
        }

        .line-number::before {
            content: counter(line);
        }

        .line-content {
            flex: 1;
            white-space: pre;
            overflow-x: auto;
            color: #e6e6e6;
        }

        /* Custom scrollbar - for everything except sidebar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--scrollbar-track);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--scrollbar-thumb);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--scrollbar-thumb-hover);
        }

        /* Hide scrollbars in sidebar and chat list */
        .sidebar, .chat-list {
            scrollbar-width: none !important; /* Firefox */
            -ms-overflow-style: none !important; /* IE and Edge */
        }

        .sidebar::-webkit-scrollbar, .chat-list::-webkit-scrollbar {
            display: none !important;
            width: 0 !important;
        }

        .thinking-container {
            padding: 15px;
            background-color: var(--bot-message-bg);
            border-radius: 15px;
            display: flex;
            align-items: center;
        }

        .thinking-dots {
            display: flex;
            gap: 4px;
        }

        .dot {
            width: 8px;
            height: 8px;
            background-color: var(--text-color);
            border-radius: 50%;
            animation: bounce 1.4s infinite ease-in-out;
        }

        .dot:nth-child(1) { animation-delay: -0.32s; }
        .dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes bounce {
            0%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
        }

        .chat-title {
            padding: 10px;
            margin: 5px 0;
            background-color: var(--chat-item-bg);
            border-radius: 8px;
            word-wrap: break-word;
            font-size: 14px;
            color: var(--text-color);
        }

        /* Hide empty chat title */
        #current-chat-title:empty {
            display: none;
            margin: 0;
            padding: 0;
        }

        /* Menu styles */
        .menu-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .menu-toggle {
            background: none;
            border: none;
            color: var(--text-color);
            cursor: pointer;
            font-size: 18px;
            background-color: var(--sidebar-bg);
            padding: 12px;
            border-radius: 50%;
            transition: all var(--transition-speed) ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .menu-toggle:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        :root.light-theme .menu-toggle {
            background-color: var(--sidebar-bg);
            color: var(--text-color);
        }

        :root.light-theme .menu-toggle:hover {
            background-color: var(--button-hover);
        }

        .dropdown-menu {
            position: absolute;
            top: 55px;
            right: 0;
            background-color: var(--sidebar-bg);
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            width: 200px;
            display: none;
            overflow: hidden;
            border: 1px solid var(--sidebar-border);
        }

        .dropdown-menu.active {
            display: block;
            animation: fadeInMenu 0.2s ease-out;
        }

        @keyframes fadeInMenu {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .menu-item {
            padding: 14px 18px;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: background-color var(--transition-speed) ease;
            border-bottom: 1px solid var(--sidebar-border);
        }

        .menu-item:last-child {
            border-bottom: none;
        }

        .menu-item:hover {
            background-color: var(--button-hover);
        }

        .menu-item i {
            margin-right: 12px;
            width: 20px;
            text-align: center;
            font-size: 16px;
        }
    </style>
</head>
<body>

    <div class="menu-container">
        <button class="menu-toggle" id="menu-toggle" title="Menu">
            <i class="fas fa-bars"></i>
        </button>
        <div class="dropdown-menu" id="dropdown-menu">
            <div class="menu-item" id="profile-btn">
                <i class="fas fa-user-circle"></i>
                <span>Profile</span>
            </div>
            <div class="menu-item" id="theme-toggle">
                <i class="fas fa-moon"></i>
                <span>Dark Mode</span>
            </div>
            <div class="menu-item">
                <i class="fas fa-user-plus"></i>
                <span>Add Account</span>
            </div>
            <div class="menu-item" id="logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                <span>Logout</span>
            </div>
        </div>
    </div>
    <div class="container">
        <div class="sidebar" id="sidebar">
            <button class="sidebar-toggle" id="sidebar-toggle" title="Toggle Sidebar">
                <i class="fas fa-chevron-left"></i>
            </button>
            <div class="user-info" id="profile-link" title="Go to Profile">
                <span class="username">
                    <div class="profile-pic">
                        {% if session.profile_pic %}
                            <img src="{{ session.profile_pic }}" alt="Profile Picture">
                        {% else %}
                            <span class="initial-avatar">{{ session.username[0] }}</span>
                        {% endif %}
                    </div>
                    <span class="full-name">{{ session.username }}</span>
                </span>
            </div>
            <button class="new-chat-btn" onclick="startNewChat()" title="New Chat">
                <i class="fas fa-plus"></i><span>New Chat</span>
            </button>
            <div id="current-chat-title" class="chat-title"></div>
            <ul id="chat-list" class="chat-list">
                {% for conversation in conversations %}
                    <li class="chat-item {% if conversation.is_saved %}saved{% endif %}" data-id="{{ conversation.id }}">
                        <div class="chat-item-content" onclick="loadConversation('{{ conversation.id }}')">
                            <span class="chat-title-text">
                                {% if conversation.is_saved %}
                                    <i class="fas fa-star"></i>
                                    <span class="chat-number">{{ loop.index }}</span>
                                    <span class="chat-title">{{ conversation.title }}</span>
                                {% else %}
                                    {{ conversation.title }}
                                {% endif %}
                            </span>
                        </div>
                        <div class="chat-actions">
                            <div class="dropdown">
                                <button type="button" class="menu-button" onclick="toggleDropdown('{{ conversation.id }}', event)">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <div id="dropdown-{{ conversation.id }}" class="chat-dropdown-menu">
                                    <div class="dropdown-item" onclick="event.stopPropagation(); editChatTitle('{{ conversation.id }}')">
                                        <i class="fas fa-edit"></i> Edit
                                    </div>
                                    <div class="dropdown-item" onclick="event.stopPropagation(); toggleSaveChat('{{ conversation.id }}')">
                                        <i class="fas fa-star"></i> {% if conversation.is_saved %}Unsave{% else %}Save{% endif %}
                                    </div>
                                    <div class="dropdown-item delete-item" onclick="event.stopPropagation(); confirmDeleteChat('{{ conversation.id }}')">
                                        <i class="fas fa-trash"></i> Delete
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                {% endfor %}
            </ul>
        </div>
        <div class="main-content">
            <div id="chat-container"></div>
            <div id="input-container">
                <input type="text" id="user-input" placeholder="Type your message...">
                <button id="send-button" onclick="sendMessage()">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <h2>Delete Confirmation</h2>
            <p>Are you sure you want to delete this chat?</p>
            <div class="modal-buttons">
                <button onclick="confirmDelete()" class="delete-btn">Delete</button>
                <button onclick="closeDeleteModal()" class="cancel-btn">Cancel</button>
            </div>
        </div>
    </div>





    <style>
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: var(--modal-bg);
            margin: 15% auto;
            padding: 20px;
            border: 1px solid var(--sidebar-border);
            width: 300px;
            border-radius: 8px;
            color: var(--text-color);
            position: relative;
        }

        .profile-modal-content {
            width: 400px;
            max-width: 90%;
        }

        .close-modal {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 24px;
            cursor: pointer;
            color: var(--text-color);
            opacity: 0.7;
        }

        .close-modal:hover {
            opacity: 1;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--sidebar-border);
            border-radius: 4px;
            background-color: var(--input-bg);
            color: var(--text-color);
        }

        textarea.form-control {
            resize: vertical;
        }

        .profile-pic-preview {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
        }

        .current-pic {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            overflow: hidden;
            background-color: var(--button-bg);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .current-pic img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .initial-preview {
            font-size: 40px;
            font-weight: bold;
            color: var(--text-color);
        }

        .save-btn {
            background-color: var(--notification-success);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }

        .modal-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }

        .delete-btn {
            background-color: var(--delete-btn);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }

        .cancel-btn {
            background-color: var(--cancel-btn);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }

        .saved {
            background-color: var(--saved-bg);
        }

        .fa-star {
            color: var(--star-color);
            margin-right: 5px;
        }

        .no-results {
            text-align: center;
            padding: 15px;
            color: #888;
            font-style: italic;
        }

        /* Dropdown Menu Styles */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-menu {
            position: absolute;
            right: 0;
            top: 100%;
            background-color: var(--sidebar-bg);
            min-width: 160px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
            z-index: 100;
            border-radius: 8px;
            overflow: hidden;
            display: none;
            margin-top: 5px;
            border: 1px solid var(--sidebar-border);
        }

        .chat-dropdown-menu {
            position: absolute; /* Back to absolute positioning */
            right: 0;
            top: 100%;
            background-color: #444444; /* Darker background for better visibility */
            width: 120px; /* Fixed width for a small box */
            box-shadow: 0 8px 16px rgba(0,0,0,0.4);
            z-index: 9999; /* Very high z-index to ensure it appears above everything */
            border-radius: 8px;
            overflow: hidden;
            display: none;
            margin-top: 5px;
            border: 1px solid #555555;
        }

        .dropdown-menu.show, .chat-dropdown-menu.show {
            display: block;
            animation: fadeIn 0.2s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .dropdown-item {
            padding: 8px 10px; /* Smaller padding for more compact look */
            text-decoration: none;
            display: flex;
            align-items: center;
            color: #ffffff; /* Brighter text for better visibility */
            cursor: pointer;
            transition: background-color 0.2s;
            border-bottom: 1px solid #555555;
            font-weight: 500; /* Slightly bolder text */
            user-select: none; /* Prevent text selection */
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            font-size: 0.9em; /* Slightly smaller font */
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item:hover {
            background-color: #555555; /* Darker hover color */
            color: #ffffff;
        }

        .dropdown-item:active {
            background-color: #666666; /* Even darker color when clicked */
        }

        .dropdown-item i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
            color: #ffffff; /* Ensure icons are also visible */
            font-size: 0.9em; /* Slightly smaller icons */
        }

        /* Ensure chat dropdown items are properly styled */
        .chat-dropdown-menu .dropdown-item {
            z-index: 9999;
            position: relative;
        }

        .delete-item {
            color: #ff6b6b !important; /* Brighter red for delete option */
        }

        .delete-item:hover {
            background-color: rgba(220, 53, 69, 0.3) !important; /* More visible hover state */
        }

        .delete-item i {
            color: #ff6b6b !important; /* Make sure the icon is also red */
        }
    </style>

    <script>
        let currentConversationId = null;
        const chatContainer = document.getElementById('chat-container');
        const userInput = document.getElementById('user-input');
        let deleteModalChatId = null;

        async function loadConversation(conversationId) {
            try {
                console.log(`Loading conversation with ID: ${conversationId}`);
                const response = await fetch(`/load_conversation/${conversationId}`);

                if (!response.ok) {
                    throw new Error(`Failed to load conversation: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                console.log('Loaded conversation data:', data);

                // Clear current chat
                chatContainer.innerHTML = '';
                currentConversationId = conversationId;

                // Add all messages
                if (data.messages && data.messages.length > 0) {
                    data.messages.forEach(msg => {
                        addMessage(msg.content, msg.is_user);
                    });
                } else {
                    console.warn('No messages found in the loaded conversation');
                }

                // Update chat title and highlight current chat
                const chatItems = document.querySelectorAll('.chat-item');
                let foundActiveChat = false;

                // If the response includes a title, use it
                const chatTitle = data.title || 'Conversation';
                document.getElementById('current-chat-title').textContent = chatTitle;

                chatItems.forEach(item => {
                    item.classList.remove('active');
                    const itemId = item.getAttribute('data-id');

                    if (itemId === conversationId) {
                        foundActiveChat = true;
                        item.classList.add('active');

                        // Update the title in the sidebar if needed
                        const titleElement = item.querySelector('.chat-title-text');
                        if (titleElement && data.title) {
                            // Check if this is a saved chat (has star icon)
                            const isSaved = item.classList.contains('saved');

                            if (isSaved) {
                                // Get the chat number (index)
                                const chatNumberEl = titleElement.querySelector('.chat-number');
                                const chatNumber = chatNumberEl ? chatNumberEl.textContent : '';

                                // Preserve the star icon and chat number
                                titleElement.innerHTML = `
                                    <i class="fas fa-star"></i>
                                    <span class="chat-number">${chatNumber}</span>
                                    <span class="chat-title">${data.title}</span>
                                `;
                            } else {
                                titleElement.textContent = data.title;
                            }
                        }
                    }
                });

                if (!foundActiveChat) {
                    console.warn(`Could not find chat item with ID ${conversationId} in the sidebar`);
                    // Try to refresh the chat list
                    await refreshChatList();
                }

                // Show thread status notification
                if (data.thread_loaded) {
                    showNotification('Thread context loaded successfully', 'success');
                } else {
                    showNotification('Starting new conversation thread', 'info');
                }

                // Scroll to bottom
                chatContainer.scrollTop = chatContainer.scrollHeight;
            } catch (error) {
                console.error('Error loading conversation:', error);
                showNotification('Error loading conversation', 'error');
            }
        }

        async function sendMessage() {
            const message = userInput.value.trim();
            if (!message) return;

            userInput.value = '';
            userInput.disabled = true;

            addMessage(message, true);

            try {
                const response = await fetch('/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message,
                        conversation_id: currentConversationId
                    })
                });

                const initialData = await response.json();
                currentConversationId = initialData.conversation_id;

                if (initialData.thinking) {
                    const thinkingContainer = addThinkingAnimation();

                    const finalResponse = await fetch('/get_response', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            message: message,
                            conversation_id: currentConversationId
                        })
                    });

                    const finalData = await finalResponse.json();
                    removeThinkingAnimation();
                    addMessage(finalData.response, false);

                    // Handle title update if provided
                    if (finalData.title_updated && finalData.new_title) {
                        updateChatTitle(currentConversationId, finalData.new_title);
                        showNotification('Chat title updated based on conversation', 'info');
                    }
                } else if (initialData.response) {
                    // Handle immediate responses (like greetings)
                    addMessage(initialData.response, false);
                }

                // Refresh chat list to show new conversation
                await refreshChatList();
            } catch (error) {
                removeThinkingAnimation();
                addMessage('Error: Could not get response from the server.', false);
            } finally {
                userInput.disabled = false;
                userInput.focus();
            }
        }

        async function refreshChatList() {
            try {
                // Save current search term if any
                const searchInput = document.getElementById('chat-search').value.toLowerCase();

                const response = await fetch('/');
                const html = await response.text();
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');

                const chatList = document.getElementById('chat-list');
                const newChatList = doc.getElementById('chat-list');

                if (newChatList) {
                    chatList.innerHTML = newChatList.innerHTML;

                    // Reattach click handlers
                    document.querySelectorAll('.chat-item').forEach((item, index) => {
                        const id = item.getAttribute('data-id');

                        // Fix click handler for the chat item content
                        const chatItemContent = item.querySelector('.chat-item-content');
                        if (chatItemContent) {
                            chatItemContent.onclick = () => loadConversation(id);
                        }

                        // Ensure saved chats have proper chat numbers
                        const titleText = item.querySelector('.chat-title-text');
                        if (titleText && item.classList.contains('saved')) {
                            const chatNumberEl = titleText.querySelector('.chat-number');
                            if (chatNumberEl) {
                                chatNumberEl.textContent = index + 1;
                            }
                        }

                        // Fix click handlers for menu items
                        const menuItems = item.querySelectorAll('.chat-menu div');
                        if (menuItems.length >= 3) {
                            menuItems[0].onclick = (e) => { e.stopPropagation(); editChatTitle(id); };
                            menuItems[1].onclick = (e) => { e.stopPropagation(); toggleSaveChat(id); };
                            menuItems[2].onclick = (e) => { e.stopPropagation(); confirmDeleteChat(id); };
                        }

                        // Make sure the menu button has the correct ID
                        const menuButton = item.querySelector('.menu-button');
                        if (menuButton) {
                            menuButton.id = 'menu-btn-' + id;
                        }

                        // Make sure the dropdown has the correct ID
                        const dropdown = item.querySelector('.dropdown-content');
                        if (dropdown) {
                            dropdown.id = 'dropdown-' + id;
                        }

                        // Highlight current chat
                        if (id == currentConversationId) {
                            item.classList.add('active');
                            const titleContent = item.querySelector('.chat-title-text').textContent.trim();
                            document.getElementById('current-chat-title').textContent = titleContent;
                        }

                        // Apply search filter if there was a search term
                        if (searchInput) {
                            const title = titleText ? titleText.textContent.toLowerCase() : '';
                            if (!title.includes(searchInput)) {
                                item.style.display = 'none';
                            }
                        }
                    });

                    // Re-apply search if there was a search term
                    if (searchInput) {
                        // Check if we need to show the no results message
                        let visibleItems = Array.from(document.querySelectorAll('.chat-item')).filter(item =>
                            item.style.display !== 'none'
                        );

                        if (visibleItems.length === 0) {
                            let noResultsMsg = document.getElementById('no-results-msg');
                            if (!noResultsMsg) {
                                noResultsMsg = document.createElement('div');
                                noResultsMsg.id = 'no-results-msg';
                                noResultsMsg.className = 'no-results';
                                noResultsMsg.textContent = 'No chats found';
                                chatList.appendChild(noResultsMsg);
                            }
                        }
                    }

                    // Set up menu buttons after refreshing the list
                    setupMenuButtons();
                }
            } catch (error) {
                console.error('Error refreshing chat list:', error);
            }
        }

        async function startNewChat() {
            currentConversationId = null;
            document.getElementById('chat-container').innerHTML = '';
            document.getElementById('current-chat-title').textContent = '';

            // Remove active class from all chat items
            document.querySelectorAll('.chat-item').forEach(item => {
                item.classList.remove('active');
            });

            // Reset the AI's chat history on the server
            try {
                await fetch('/reset_chat', {
                    method: 'POST'
                });
                showNotification('Started new conversation', 'info');
            } catch (error) {
                console.error('Error resetting chat:', error);
            }
        }

        // Add some CSS for active chat
        const style = document.createElement('style');
        style.textContent = `
            .chat-item.active {
                background-color: var(--active-chat-bg);
                border-left: 4px solid var(--active-chat-border);
            }
        `;
        document.head.appendChild(style);

        function addThinkingAnimation() {
            const container = document.createElement('div');
            container.className = 'message-container bot-container thinking-animation';

            const thinking = document.createElement('div');
            thinking.className = 'thinking-container';

            const dots = document.createElement('div');
            dots.className = 'thinking-dots';

            for (let i = 0; i < 3; i++) {
                const dot = document.createElement('div');
                dot.className = 'dot';
                dots.appendChild(dot);
            }

            thinking.appendChild(dots);
            container.appendChild(thinking);
            chatContainer.appendChild(container);
            chatContainer.scrollTop = chatContainer.scrollHeight;
            return container;
        }

        function removeThinkingAnimation() {
            const thinking = document.querySelector('.thinking-animation');
            if (thinking) thinking.remove();
        }

        userInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        function addMessage(message, isUser) {
            const messageContainer = document.createElement('div');
            messageContainer.className = `message-container ${isUser ? 'user-container' : 'bot-container'}`;

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'bot-message'}`;

            // Check if the message contains Python code
            if (!isUser && message.includes('```python')) {
                // Process the message to extract and format code blocks
                const processedMessage = processPythonCodeBlocks(message);
                messageDiv.innerHTML = processedMessage;
            } else {
                messageDiv.textContent = message;
            }

            messageContainer.appendChild(messageDiv);
            chatContainer.appendChild(messageContainer);
            chatContainer.scrollTop = chatContainer.scrollHeight;

            // Ensure sidebar width is maintained
            maintainSidebarWidth();

            // Add event listeners to copy buttons if any were created
            document.querySelectorAll('.copy-button').forEach(button => {
                button.addEventListener('click', function() {
                    const codeContent = this.closest('.code-container').querySelector('.code-content');
                    const codeText = Array.from(codeContent.querySelectorAll('.line-content'))
                        .map(line => line.textContent)
                        .join('\n');

                    copyToClipboard(codeText, this);
                });
            });
        }

        function processPythonCodeBlocks(message) {
            // Split the message by Python code blocks
            const parts = message.split(/```python|```/);
            let result = '';

            for (let i = 0; i < parts.length; i++) {
                if (i % 2 === 0) {
                    // This is regular text
                    result += parts[i];
                } else {
                    // This is a Python code block
                    const code = parts[i].trim();
                    const lines = code.split('\n');

                    // Create the code container
                    result += `
                    <div class="code-container">
                        <div class="code-header">
                            <div class="code-language">Python</div>
                            <button class="copy-button">Copy Code</button>
                        </div>
                        <ul class="code-content">
                    `;

                    // Add each line with line number
                    lines.forEach(line => {
                        result += `
                            <li class="code-line">
                                <span class="line-number"></span>
                                <span class="line-content">${escapeHtml(line)}</span>
                            </li>
                        `;
                    });

                    result += `
                        </ul>
                    </div>
                    `;
                }
            }

            return result;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function copyToClipboard(text, button) {
            navigator.clipboard.writeText(text).then(() => {
                // Change button text temporarily
                const originalText = button.textContent;
                button.textContent = 'Copied!';

                // Show notification
                showNotification('Code copied to clipboard!', 'success');

                // Reset button text after 2 seconds
                setTimeout(() => {
                    button.textContent = originalText;
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy text: ', err);
                showNotification('Failed to copy code', 'error');
            });
        }

        // Function to ensure sidebar width is maintained
        function maintainSidebarWidth() {
            const sidebar = document.getElementById('sidebar');
            if (sidebar.classList.contains('collapsed')) {
                sidebar.style.width = '60px';
                sidebar.style.minWidth = '60px';
            } else {
                sidebar.style.width = '250px';
                sidebar.style.minWidth = '250px';
            }
        }

        // Add event listeners for all menu buttons when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Add click event listeners to all menu buttons
            setupMenuButtons();
        });

        // Function to toggle dropdown visibility
        function toggleDropdown(chatId, event) {
            // Prevent the event from bubbling up to document
            if (event) {
                event.stopPropagation();
            }

            // Close all dropdowns first
            document.querySelectorAll('.chat-dropdown-menu').forEach(menu => {
                if (menu.id !== `dropdown-${chatId}`) {
                    menu.classList.remove('show');
                }
            });

            // Toggle the clicked dropdown
            const dropdown = document.getElementById(`dropdown-${chatId}`);
            if (dropdown) {
                // Force show instead of toggle to ensure it appears
                if (!dropdown.classList.contains('show')) {
                    dropdown.classList.add('show');
                } else {
                    dropdown.classList.remove('show');
                    return; // Exit early if we're hiding the dropdown
                }

                // Let the dropdown position itself naturally with absolute positioning
                console.log('Toggled dropdown for chat ID:', chatId);
            } else {
                console.error('Dropdown element not found for chat ID:', chatId);
            }
        }

        // Function to set up menu buttons (for compatibility with existing code)
        function setupMenuButtons() {
            // Add click event to document to close dropdowns when clicking outside
            document.addEventListener('click', function(event) {
                if (!event.target.closest('.dropdown')) {
                    document.querySelectorAll('.chat-dropdown-menu').forEach(menu => {
                        menu.classList.remove('show');
                    });
                }
            });

            console.log('Dropdown menu handlers set up');
        }

        // Function to update chat title in the UI
        function updateChatTitle(chatId, newTitle) {
            const chatItem = document.querySelector(`.chat-item[data-id="${chatId}"]`);
            if (chatItem) {
                const titleElement = chatItem.querySelector('.chat-title-text');
                if (titleElement) {
                    // Check if this is a saved chat (has star icon)
                    const isSaved = chatItem.classList.contains('saved');

                    if (isSaved) {
                        // Get the chat number (index)
                        const chatNumberEl = titleElement.querySelector('.chat-number');
                        let chatNumber = chatNumberEl ? chatNumberEl.textContent : '';

                        // If no chat number, calculate it based on position
                        if (!chatNumber) {
                            const savedChats = document.querySelectorAll('.chat-item.saved');
                            savedChats.forEach((item, index) => {
                                if (item === chatItem) {
                                    chatNumber = index + 1;
                                }
                            });
                        }

                        // Preserve the star icon and chat number
                        titleElement.innerHTML = `
                            <i class="fas fa-star"></i>
                            <span class="chat-number">${chatNumber}</span>
                            <span class="chat-title">${newTitle}</span>
                        `;
                    } else {
                        titleElement.textContent = newTitle;
                    }

                    // Update current chat title if this is the active chat
                    if (chatId === currentConversationId) {
                        document.getElementById('current-chat-title').textContent = newTitle;
                    }
                }
            }
        }

        async function editChatTitle(chatId) {
            const chatItem = document.querySelector(`.chat-item[data-id="${chatId}"]`);
            const titleElement = chatItem.querySelector('.chat-title-text');

            // Get current title, handling both saved and unsaved chats
            let currentTitle;
            if (chatItem.classList.contains('saved')) {
                const titleSpan = titleElement.querySelector('.chat-title');
                currentTitle = titleSpan ? titleSpan.textContent.trim() : titleElement.textContent.trim();
            } else {
                currentTitle = titleElement.textContent.trim();
            }

            const newTitle = prompt('Enter new chat title:', currentTitle);

            if (newTitle && newTitle !== currentTitle) {
                try {
                    const response = await fetch('/update_chat_title', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            chat_id: chatId,
                            new_title: newTitle
                        })
                    });

                    if (response.ok) {
                        updateChatTitle(chatId, newTitle);
                    }
                } catch (error) {
                    console.error('Error updating chat title:', error);
                }
            }
        }

        async function deleteChat(chatId) {
            if (confirm('Are you sure you want to delete this chat?')) {
                try {
                    const response = await fetch(`/delete_chat/${chatId}`, {
                        method: 'DELETE'
                    });

                    if (response.ok) {
                        if (chatId === currentConversationId) {
                            startNewChat();
                        }
                        await refreshChatList();
                    }
                } catch (error) {
                    console.error('Error deleting chat:', error);
                }
            }
        }

        async function toggleSaveChat(chatId) {
            try {
                const response = await fetch(`/toggle_save_chat/${chatId}`, {
                    method: 'POST'
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('Toggle save response:', data);

                    // Update the chat item in the UI immediately
                    const chatItem = document.querySelector(`.chat-item[data-id="${chatId}"]`);
                    if (chatItem) {
                        // Get the current title
                        const titleElement = chatItem.querySelector('.chat-title-text');
                        const title = titleElement ? titleElement.textContent.trim() : 'Chat';

                        if (data.is_saved) {
                            // Chat was saved
                            chatItem.classList.add('saved');

                            // Get the next available number for saved chats
                            const savedChats = document.querySelectorAll('.chat-item.saved');
                            const chatNumber = savedChats.length;

                            // Update the title format for saved chat
                            titleElement.innerHTML = `
                                <i class="fas fa-star"></i>
                                <span class="chat-number">${chatNumber}</span>
                                <span class="chat-title">${title}</span>
                            `;

                            showNotification('Chat saved', 'success');
                        } else {
                            // Chat was unsaved
                            chatItem.classList.remove('saved');
                            titleElement.textContent = title;
                            showNotification('Chat unsaved', 'info');
                        }
                    }

                    // Refresh the chat list to ensure proper ordering and numbering
                    await refreshChatList();
                }
            } catch (error) {
                console.error('Error toggling save status:', error);
                showNotification('Error updating save status', 'error');
            }
        }

        function confirmDeleteChat(chatId) {
            deleteModalChatId = chatId;
            document.getElementById('deleteModal').style.display = 'block';
        }

        function closeDeleteModal() {
            document.getElementById('deleteModal').style.display = 'none';
            deleteModalChatId = null;
        }

        async function confirmDelete() {
            if (deleteModalChatId) {
                await deleteChat(deleteModalChatId);
                closeDeleteModal();
            }
        }



        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.dropdown')) {
                document.querySelectorAll('.dropdown-content, .chat-dropdown-menu').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
            }
        });

        // Close modals when clicking outside
        window.onclick = function(event) {
            // Close delete modal if clicking outside
            const deleteModal = document.getElementById('deleteModal');
            if (event.target == deleteModal) {
                closeDeleteModal();
            }

            // Close dropdowns when clicking outside
            if (!event.target.closest('.dropdown') && !event.target.closest('.menu-button')) {
                document.querySelectorAll('.chat-dropdown-menu').forEach(menu => {
                    menu.classList.remove('show');
                });
            }
        }

        function showNotification(message, type) {
            // Just log to console instead of showing notifications
            console.log(`${type}: ${message}`);
        }

        // Sidebar toggle functionality
        document.getElementById('sidebar-toggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('collapsed');

            // Change the icon based on the sidebar state
            const icon = this.querySelector('i');
            if (sidebar.classList.contains('collapsed')) {
                icon.classList.remove('fa-chevron-left');
                icon.classList.add('fa-chevron-right');
            } else {
                icon.classList.remove('fa-chevron-right');
                icon.classList.add('fa-chevron-left');
            }

            // Ensure sidebar width is maintained
            maintainSidebarWidth();

            // Save the sidebar state in localStorage
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        });

        // Menu toggle functionality
        document.getElementById('menu-toggle').addEventListener('click', function() {
            const dropdownMenu = document.getElementById('dropdown-menu');
            dropdownMenu.classList.toggle('active');
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            const menuContainer = document.querySelector('.menu-container');
            const dropdownMenu = document.getElementById('dropdown-menu');

            if (!menuContainer.contains(event.target) && dropdownMenu.classList.contains('active')) {
                dropdownMenu.classList.remove('active');
            }
        });

        // Theme toggle functionality
        document.getElementById('theme-toggle').addEventListener('click', function() {
            const root = document.documentElement;
            const themeIcon = this.querySelector('i');
            const themeText = this.querySelector('span');

            if (root.classList.contains('light-theme')) {
                // Switch to dark theme
                root.classList.remove('light-theme');
                themeIcon.classList.remove('fa-sun');
                themeIcon.classList.add('fa-moon');
                themeText.textContent = 'Dark Mode';
                localStorage.setItem('theme', 'dark');
                showNotification('Dark mode activated', 'info');
            } else {
                // Switch to light theme
                root.classList.add('light-theme');
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
                themeText.textContent = 'Light Mode';
                localStorage.setItem('theme', 'light');
                showNotification('Light mode (gray & white) activated', 'info');
            }
        });

        // Load sidebar state and theme from localStorage on page load
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const sidebarIcon = sidebarToggle.querySelector('i');
            const themeToggle = document.getElementById('theme-toggle');
            const themeIcon = themeToggle.querySelector('i');
            const themeText = themeToggle.querySelector('span');
            const root = document.documentElement;

            // Load sidebar state
            if (localStorage.getItem('sidebarCollapsed') === 'true') {
                sidebar.classList.add('collapsed');
                sidebarIcon.classList.remove('fa-chevron-left');
                sidebarIcon.classList.add('fa-chevron-right');
            }

            // Load theme preference
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'light') {
                root.classList.add('light-theme');
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
                themeText.textContent = 'Light Mode';
            }

            // Ensure sidebar width is maintained on page load
            maintainSidebarWidth();

            // Also maintain sidebar width when window is resized
            window.addEventListener('resize', maintainSidebarWidth);

            // Add click handler for Profile button in menu
            document.getElementById('profile-btn').addEventListener('click', function() {
                window.location.href = '/profile';
            });

            // Add click handler for profile link in sidebar
            document.getElementById('profile-link').addEventListener('click', function() {
                window.location.href = '/profile';
            });

            // Add click handler for Add Account menu item
            document.querySelector('.menu-item:nth-child(3)').addEventListener('click', function() {
                showNotification('Add Account feature coming soon', 'info');
            });

            // Add click handler for Logout button
            document.getElementById('logout-btn').addEventListener('click', function() {
                window.location.href = '/logout';
            });

            // Add event listener for profile picture preview
            const profilePictureInput = document.getElementById('profile-picture');
            if (profilePictureInput) {
                profilePictureInput.addEventListener('change', function(event) {
                    const file = event.target.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const previewContainer = document.querySelector('.current-pic');
                            previewContainer.innerHTML = `<img src="${e.target.result}" alt="Profile Picture Preview" id="profile-pic-preview">`;
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }
        });


    </script>
</body>
</html>




























