<!DOCTYPE html>
<html>
<head>
    <title>Voyager AI - Explore the Universe of Conversation</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            /* Dark theme (default) */
            --bg-color: #0a0a0a;
            --text-color: #ffffff;
            --primary-color: #4a90e2;
            --secondary-color: #7b68ee;
            --accent-color: #ff6b6b;
            --button-bg: #1e3a8a;
            --button-hover: #2563eb;
            --container-bg: rgba(26, 26, 26, 0.8);
            --box-shadow: rgba(0, 0, 0, 0.5);
            --transition-speed: 0.3s;
            --star-color: #ffffff;
            --nebula-color: rgba(123, 104, 238, 0.3);
        }

        /* Light theme (pinkish white) */
        :root.light-theme {
            --bg-color: #fff5f7;
            --text-color: #333333;
            --primary-color: #ec407a;
            --secondary-color: #f48fb1;
            --accent-color: #ff4081;
            --button-bg: #f8bbd0;
            --button-hover: #f48fb1;
            --container-bg: rgba(255, 235, 238, 0.9);
            --box-shadow: rgba(0, 0, 0, 0.1);
            --star-color: #ec407a;
            --nebula-color: rgba(244, 143, 177, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            transition: all var(--transition-speed) ease;
        }

        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            background: var(--bg-color);
            color: var(--text-color);
            height: 100vh;
            overflow: hidden;
            position: relative;
            transition: background-color var(--transition-speed) ease, color var(--transition-speed) ease;
            opacity: 0;
            animation: fadeIn 0.8s ease-out forwards;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Space background */
        .space-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(ellipse at center,
                var(--nebula-color) 0%,
                transparent 50%),
                var(--bg-color);
            z-index: -2;
            transition: background var(--transition-speed) ease;
        }

        /* Stars */
        .stars {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        .star {
            position: absolute;
            background: var(--star-color);
            border-radius: 50%;
            animation: twinkle 2s infinite alternate;
            transition: background-color var(--transition-speed) ease;
        }

        .star.small {
            width: 1px;
            height: 1px;
        }

        .star.medium {
            width: 2px;
            height: 2px;
        }

        .star.large {
            width: 3px;
            height: 3px;
        }

        @keyframes twinkle {
            0% { opacity: 0.3; }
            100% { opacity: 1; }
        }

        /* Spacecraft animation */
        .spacecraft {
            position: absolute;
            top: 20%;
            left: -100px;
            font-size: 60px;
            color: var(--primary-color);
            animation: fly 15s linear infinite;
            z-index: 1;
            transition: color var(--transition-speed) ease;
        }

        @keyframes fly {
            0% {
                left: -100px;
                top: 20%;
                transform: rotate(0deg);
            }
            25% {
                left: 25%;
                top: 15%;
                transform: rotate(5deg);
            }
            50% {
                left: 50%;
                top: 25%;
                transform: rotate(-5deg);
            }
            75% {
                left: 75%;
                top: 10%;
                transform: rotate(3deg);
            }
            100% {
                left: calc(100% + 100px);
                top: 20%;
                transform: rotate(0deg);
            }
        }

        /* Theme toggle button */
        .theme-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            background: var(--container-bg);
            border: 2px solid var(--primary-color);
            color: var(--text-color);
            cursor: pointer;
            font-size: 20px;
            padding: 12px;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-speed) ease, transform 0.2s ease;
            z-index: 10;
            box-shadow: 0 4px 15px var(--box-shadow);
        }

        .theme-toggle:hover {
            transform: scale(1.1) rotate(15deg);
            background: var(--primary-color);
            color: white;
            box-shadow: 0 6px 20px var(--box-shadow);
        }

        .theme-toggle i {
            transition: transform var(--transition-speed) ease;
        }

        /* Logout button */
        .logout-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: var(--container-bg);
            border: 2px solid var(--accent-color);
            color: var(--text-color);
            cursor: pointer;
            font-size: 14px;
            padding: 12px 20px;
            border-radius: 25px;
            text-decoration: none;
            transition: all var(--transition-speed) ease, transform 0.2s ease;
            z-index: 10;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px var(--box-shadow);
            backdrop-filter: blur(10px);
        }

        .logout-btn:hover {
            background: var(--accent-color);
            color: white;
            transform: scale(1.05) translateY(-2px);
            box-shadow: 0 6px 20px var(--box-shadow);
        }

        /* Main container */
        .landing-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            text-align: center;
            padding: 20px;
            position: relative;
            z-index: 2;
        }

        .hero-section {
            background: var(--container-bg);
            border-radius: 20px;
            padding: 60px 40px;
            box-shadow: 0 20px 40px var(--box-shadow);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            max-width: 600px;
            width: 100%;
            transition: background-color var(--transition-speed) ease, box-shadow var(--transition-speed) ease, border-color var(--transition-speed) ease;
        }

        .chat-box {
            background: var(--container-bg);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px var(--box-shadow);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            max-width: 400px;
            width: 100%;
            text-align: center;
            transition: background-color var(--transition-speed) ease, box-shadow var(--transition-speed) ease, border-color var(--transition-speed) ease;
        }

        .chat-box h2 {
            font-size: 32px;
            margin-bottom: 20px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: bold;
        }

        .chat-box .btn {
            font-size: 20px;
            padding: 20px 40px;
            width: 100%;
        }

        .logo {
            font-size: 48px;
            margin-bottom: 20px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: bold;
        }

        .tagline {
            font-size: 24px;
            margin-bottom: 15px;
            color: var(--text-color);
        }

        .description {
            font-size: 16px;
            margin-bottom: 40px;
            color: var(--text-color);
            opacity: 0.8;
            line-height: 1.6;
        }

        .action-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all var(--transition-speed) ease, transform 0.2s ease;
            min-width: 160px;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(74, 144, 226, 0.4);
        }

        .btn-secondary {
            background: transparent;
            color: var(--text-color);
            border: 2px solid var(--primary-color);
        }

        .btn-secondary:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .hero-section {
                padding: 40px 20px;
                margin: 20px;
            }

            .logo {
                font-size: 36px;
            }

            .tagline {
                font-size: 20px;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 250px;
            }

            .spacecraft {
                font-size: 40px;
            }
        }

        /* Floating particles */
        .particle {
            position: absolute;
            background: var(--primary-color);
            border-radius: 50%;
            opacity: 0.6;
            animation: float 6s ease-in-out infinite;
            transition: background-color var(--transition-speed) ease;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <button class="theme-toggle" id="theme-toggle" title="Toggle Light/Dark Mode">
        <i class="fas fa-moon"></i>
    </button>

    {% if is_logged_in %}
        <a href="{{ url_for('logout') }}" class="logout-btn" title="Logout">
            <i class="fas fa-sign-out-alt"></i>
            Logout
        </a>
    {% endif %}

    <div class="space-background"></div>
    <div class="stars" id="stars"></div>

    <div class="spacecraft">
        <i class="fas fa-rocket"></i>
    </div>

    <div class="landing-container">
        {% if is_logged_in %}
            <div class="chat-box">
                <h2>
                    <i class="fas fa-comments"></i> To Chat
                </h2>
                <a href="{{ url_for('dashboard') }}" class="btn btn-primary">
                    <i class="fas fa-rocket"></i>
                    Launch Chat
                </a>
            </div>
        {% else %}
            <div class="hero-section">
                <div class="logo">
                    <i class="fas fa-robot"></i> Voyager AI
                </div>
                <h1 class="tagline">Explore the Universe of Conversation</h1>
                <p class="description">
                    Embark on an interstellar journey of knowledge and discovery.
                    Chat with our advanced AI companion and explore limitless possibilities
                    across the cosmos of information.
                </p>
                <div class="action-buttons">
                    <a href="{{ url_for('login') }}" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i>
                        Sign In
                    </a>
                    <a href="{{ url_for('register') }}" class="btn btn-secondary">
                        <i class="fas fa-user-plus"></i>
                        Sign Up
                    </a>
                </div>
            </div>
        {% endif %}
    </div>

    <script>
        // Create stars
        function createStars() {
            const starsContainer = document.getElementById('stars');
            const starCount = 150;

            for (let i = 0; i < starCount; i++) {
                const star = document.createElement('div');
                star.className = 'star';

                // Random size
                const sizes = ['small', 'medium', 'large'];
                const randomSize = sizes[Math.floor(Math.random() * sizes.length)];
                star.classList.add(randomSize);

                // Random position
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';

                // Random animation delay
                star.style.animationDelay = Math.random() * 2 + 's';

                starsContainer.appendChild(star);
            }
        }

        // Create floating particles
        function createParticles() {
            const particleCount = 20;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';

                // Random size
                const size = Math.random() * 4 + 2;
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';

                // Random position
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';

                // Random animation delay and duration
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 4) + 's';

                document.body.appendChild(particle);
            }
        }

        // Theme toggle functionality
        document.getElementById('theme-toggle').addEventListener('click', function() {
            const root = document.documentElement;
            const themeIcon = this.querySelector('i');
            const button = this;

            // Add a brief loading effect
            button.style.transform = 'scale(0.9) rotate(180deg)';

            setTimeout(() => {
                if (root.classList.contains('light-theme')) {
                    // Switch to dark theme
                    root.classList.remove('light-theme');
                    themeIcon.classList.remove('fa-sun');
                    themeIcon.classList.add('fa-moon');
                    localStorage.setItem('theme', 'dark');
                } else {
                    // Switch to light theme
                    root.classList.add('light-theme');
                    themeIcon.classList.remove('fa-moon');
                    themeIcon.classList.add('fa-sun');
                    localStorage.setItem('theme', 'light');
                }

                // Reset button transform
                setTimeout(() => {
                    button.style.transform = '';
                }, 100);
            }, 150);
        });

        // Load theme preference on page load
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggle = document.getElementById('theme-toggle');
            const themeIcon = themeToggle.querySelector('i');
            const root = document.documentElement;

            // Load theme preference
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'light') {
                root.classList.add('light-theme');
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
            }

            // Initialize animations
            createStars();
            createParticles();
        });
    </script>
</body>
</html>
