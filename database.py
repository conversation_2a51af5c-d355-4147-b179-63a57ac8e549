import sqlite3
from datetime import datetime
import hashlib

def init_db():
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()

    # Check if users table exists
    c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
    table_exists = c.fetchone()

    # Only create tables if they don't exist
    if not table_exists:
        # Create users table
        c.execute('''
            CREATE TABLE users
            (id INTEGER PRIMARY KEY AUTOINCREMENT,
             username TEXT UNIQUE,
             email TEXT UNIQUE,
             password TEXT,
             created_at TIMESTAMP,
             profile_pic TEXT,
             birthdate TEXT,
             hobbies TEXT)
        ''')

        # Create social accounts table
        c.execute('''
            CREATE TABLE social_accounts
            (id INTEGER PRIMARY KEY AUTOINCREMENT,
             user_id INTEGER,
             platform TEXT,
             platform_user_id TEXT,
             username TEXT,
             access_token TEXT,
             refresh_token TEXT,
             token_expires_at TIMESTAMP,
             created_at TIMESTAMP,
             FOREIGN KEY (user_id) REFERENCES users (id),
             UNIQUE(user_id, platform))
        ''')

        # Create conversations table with is_saved column and user_id
        c.execute('''
            CREATE TABLE conversations
            (id INTEGER PRIMARY KEY AUTOINCREMENT,
             title TEXT,
             created_at TIMESTAMP,
             last_updated TIMESTAMP,
             is_saved BOOLEAN DEFAULT 0,
             user_id INTEGER,
             FOREIGN KEY (user_id) REFERENCES users (id))
        ''')

        # Create messages table
        c.execute('''
            CREATE TABLE messages
            (id INTEGER PRIMARY KEY AUTOINCREMENT,
             conversation_id INTEGER,
             content TEXT,
             is_user BOOLEAN,
             timestamp TIMESTAMP,
             FOREIGN KEY (conversation_id) REFERENCES conversations (id))
        ''')

        # Create threads table to store AI model conversation threads
        c.execute('''
            CREATE TABLE threads
            (id INTEGER PRIMARY KEY AUTOINCREMENT,
             conversation_id INTEGER,
             thread_data TEXT,
             updated_at TIMESTAMP,
             FOREIGN KEY (conversation_id) REFERENCES conversations (id))
        ''')

        conn.commit()
        print("Database tables created successfully")
    else:
        print("Database tables already exist")
    conn.close()

def create_conversation(title):
    # This function is kept for backward compatibility
    # but should not be used directly anymore
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    now = datetime.now()
    c.execute('INSERT INTO conversations (title, created_at, last_updated) VALUES (?, ?, ?)',
              (title, now, now))
    conversation_id = c.lastrowid
    conn.commit()
    conn.close()
    return conversation_id

def save_message(conversation_id, content, is_user):
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    now = datetime.now()
    c.execute('INSERT INTO messages (conversation_id, content, is_user, timestamp) VALUES (?, ?, ?, ?)',
              (conversation_id, content, is_user, now))
    c.execute('UPDATE conversations SET last_updated = ? WHERE id = ?',
              (now, conversation_id))
    conn.commit()
    conn.close()

def get_conversations():
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    # First get saved conversations, then others, both ordered by last_updated
    c.execute('''
        SELECT id, title, created_at, is_saved
        FROM conversations
        ORDER BY is_saved DESC, last_updated DESC
    ''')
    conversations = c.fetchall()
    conn.close()
    return conversations

def get_conversation_messages(conversation_id):
    conn = sqlite3.connect('chats.db')
    # Set timeout to avoid database locks
    conn.execute('PRAGMA busy_timeout = 5000')
    # Use WAL mode for better concurrency
    conn.execute('PRAGMA journal_mode = WAL')
    c = conn.cursor()

    try:
        # Add index hint for better performance
        c.execute('''
            SELECT content, is_user, timestamp
            FROM messages
            WHERE conversation_id = ?
            ORDER BY timestamp
        ''', (conversation_id,))
        messages = c.fetchall()
        return messages
    except Exception as e:
        print(f"Error getting conversation messages: {str(e)}")
        return []
    finally:
        conn.close()

def update_conversation_title(conversation_id, new_title):
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('UPDATE conversations SET title = ? WHERE id = ?',
              (new_title, conversation_id))
    conn.commit()
    conn.close()

def delete_conversation(conversation_id):
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    # Delete threads and messages first due to foreign key constraint
    c.execute('DELETE FROM threads WHERE conversation_id = ?', (conversation_id,))
    c.execute('DELETE FROM messages WHERE conversation_id = ?', (conversation_id,))
    c.execute('DELETE FROM conversations WHERE id = ?', (conversation_id,))
    conn.commit()
    conn.close()

def toggle_save_conversation(conversation_id):
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('''
        UPDATE conversations
        SET is_saved = CASE WHEN is_saved = 0 THEN 1 ELSE 0 END
        WHERE id = ?
    ''', (conversation_id,))
    conn.commit()
    conn.close()

# User-related functions
def hash_password(password):
    """Hash a password for storing."""
    return hashlib.sha256(password.encode()).hexdigest()

def register_user(username, email, password):
    """Register a new user."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    now = datetime.now()

    try:
        c.execute('''
            INSERT INTO users
            (username, email, password, created_at, profile_pic, birthdate, hobbies)
            VALUES (?, ?, ?, ?, NULL, NULL, NULL)
        ''', (username, email, hash_password(password), now))
        conn.commit()
        user_id = c.lastrowid
        conn.close()
        return user_id
    except sqlite3.IntegrityError as e:
        # Username or email already exists
        print(f"Registration error: {e}")
        conn.close()
        return None

def get_user_by_username(username):
    """Get user by username."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('SELECT id, username, email, password FROM users WHERE username = ?', (username,))
    user = c.fetchone()
    conn.close()
    return user

def get_user_by_email(email):
    """Get user by email."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('SELECT id, username, email, password FROM users WHERE email = ?', (email,))
    user = c.fetchone()
    conn.close()
    return user

def verify_user(username_or_email, password):
    """Verify user credentials."""
    # Check if input is email or username
    if '@' in username_or_email:
        user = get_user_by_email(username_or_email)
        print(f"Looking up user by email: {username_or_email}, found: {user is not None}")
    else:
        user = get_user_by_username(username_or_email)
        print(f"Looking up user by username: {username_or_email}, found: {user is not None}")

    if not user:
        print("User not found")
        return None

    hashed_password = hash_password(password)
    stored_password = user[3]

    print(f"Password check: {hashed_password == stored_password}")

    if hashed_password == stored_password:
        return user[0]  # Return user ID if credentials are valid
    return None

def get_user_by_id(user_id):
    """Get user by ID."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('SELECT id, username, email, password FROM users WHERE id = ?', (user_id,))
    user = c.fetchone()
    conn.close()
    return user

def create_conversation_for_user(title, user_id):
    """Create a conversation for a specific user."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    now = datetime.now()
    c.execute('INSERT INTO conversations (title, created_at, last_updated, user_id) VALUES (?, ?, ?, ?)',
              (title, now, now, user_id))
    conversation_id = c.lastrowid
    conn.commit()
    conn.close()
    return conversation_id

def get_user_conversations(user_id):
    """Get all conversations for a user."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('''
        SELECT id, title, created_at, is_saved
        FROM conversations
        WHERE user_id = ?
        ORDER BY is_saved DESC, last_updated DESC
    ''', (user_id,))
    conversations = c.fetchall()
    conn.close()
    return conversations

def get_message_count(conversation_id):
    """Get the number of messages in a conversation."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('SELECT COUNT(*) FROM messages WHERE conversation_id = ?', (conversation_id,))
    count = c.fetchone()[0]
    conn.close()
    return count

def get_conversation_summary(conversation_id):
    """Get a summary of the conversation for title generation."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    # Get the last 10 messages to summarize the conversation
    c.execute('''
        SELECT content, is_user FROM messages
        WHERE conversation_id = ?
        ORDER BY timestamp DESC LIMIT 10
    ''', (conversation_id,))
    messages = c.fetchall()
    conn.close()

    # Format the messages for the AI to process
    formatted_messages = []
    for content, is_user in reversed(messages):  # Reverse to get chronological order
        role = "User" if is_user else "AI"
        # Truncate long messages
        if len(content) > 100:
            content = content[:100] + "..."
        formatted_messages.append(f"{role}: {content}")

    return "\n".join(formatted_messages)

# Social account functions
def get_social_accounts(user_id):
    """Get all social accounts for a user."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('''
        SELECT platform, username, platform_user_id, access_token, refresh_token, token_expires_at
        FROM social_accounts
        WHERE user_id = ?
    ''', (user_id,))
    accounts = c.fetchall()
    conn.close()
    return accounts

def get_social_account(user_id, platform):
    """Get a specific social account for a user."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('''
        SELECT platform, username, platform_user_id, access_token, refresh_token, token_expires_at
        FROM social_accounts
        WHERE user_id = ? AND platform = ?
    ''', (user_id, platform))
    account = c.fetchone()
    conn.close()
    return account

def save_social_account(user_id, platform, platform_user_id, username, access_token, refresh_token=None, token_expires_at=None):
    """Save or update a social account for a user."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    now = datetime.now()

    # Check if account already exists
    c.execute('SELECT id FROM social_accounts WHERE user_id = ? AND platform = ?', (user_id, platform))
    existing = c.fetchone()

    if existing:
        # Update existing account
        c.execute('''
            UPDATE social_accounts
            SET platform_user_id = ?, username = ?, access_token = ?, refresh_token = ?, token_expires_at = ?
            WHERE user_id = ? AND platform = ?
        ''', (platform_user_id, username, access_token, refresh_token, token_expires_at, user_id, platform))
    else:
        # Create new account
        c.execute('''
            INSERT INTO social_accounts
            (user_id, platform, platform_user_id, username, access_token, refresh_token, token_expires_at, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (user_id, platform, platform_user_id, username, access_token, refresh_token, token_expires_at, now))

    conn.commit()
    conn.close()
    return True

def delete_social_account(user_id, platform):
    """Delete a social account for a user."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('DELETE FROM social_accounts WHERE user_id = ? AND platform = ?', (user_id, platform))
    conn.commit()
    conn.close()
    return c.rowcount > 0

def update_user_profile(user_id, profile_data):
    """Update user profile information."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()

    # Build the update query dynamically based on provided fields
    fields = []
    values = []

    for field, value in profile_data.items():
        if field in ['username', 'email', 'profile_pic', 'birthdate', 'hobbies']:
            fields.append(f"{field} = ?")
            values.append(value)

    if not fields:
        conn.close()
        return False

    query = f"UPDATE users SET {', '.join(fields)} WHERE id = ?"
    values.append(user_id)

    c.execute(query, values)
    conn.commit()
    conn.close()
    return True

def get_user_profile(user_id):
    """Get complete user profile information."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('''
        SELECT id, username, email, created_at, profile_pic, birthdate, hobbies
        FROM users
        WHERE id = ?
    ''', (user_id,))
    user = c.fetchone()
    conn.close()

    if not user:
        return None

    # Format as dictionary
    user_dict = {
        'id': user[0],
        'username': user[1],
        'email': user[2],
        'created_at': user[3],
        'profile_pic': user[4],
        'birthdate': user[5],
        'hobbies': user[6]
    }

    return user_dict
