import os
from groq import <PERSON><PERSON><PERSON>
from typing import List, Dict

class GroqChatbot:
    def __init__(self, api_key: str):
        self.client = Groq(api_key=api_key)
        self.model = "mixtral-8x7b-32768"  # <PERSON><PERSON>q's recommended model
        self.conversation_history: List[Dict] = []
        
    def generate_response(self, user_input: str) -> str:
        # Add user message to conversation history
        self.conversation_history.append({
            "role": "user",
            "content": user_input
        })
        
        try:
            # Generate completion using Groq
            completion = self.client.chat.completions.create(
                model=self.model,
                messages=self.conversation_history,
                temperature=0.7,
                max_tokens=1024,
                top_p=1,
                stream=False
            )
            
            # Get the response
            response = completion.choices[0].message.content
            
            # Add assistant's response to conversation history
            self.conversation_history.append({
                "role": "assistant",
                "content": response
            })
            
            return response
            
        except Exception as e:
            return f"Error generating response: {str(e)}"
    
    def clear_history(self):
        """Clear the conversation history"""
        self.conversation_history = []

def main():
    # Get API key from environment variable
    api_key = os.getenv("GROQ_API_KEY")
    if not api_key:
        print("Please set the GROQ_API_KEY environment variable")
        return
    
    # Initialize chatbot
    chatbot = GroqChatbot(api_key)
    print("Chatbot: Hi! I'm powered by Groq AI. Type 'quit' to exit or 'clear' to reset our conversation.")
    
    while True:
        user_input = input("You: ").strip()
        
        if user_input.lower() == 'quit':
            print("Chatbot: Goodbye!")
            break
        
        if user_input.lower() == 'clear':
            chatbot.clear_history()
            print("Chatbot: Conversation history cleared!")
            continue
            
        if user_input:
            response = chatbot.generate_response(user_input)
            print(f"Chatbot: {response}")

if __name__ == "__main__":
    main()